/**
 * Code Property Graph (CPG) types for Solidity contract analysis
 * Supports deep state management, taint analysis, and control flow
 */

export interface SourceLocation {
  start: number;
  length: number;
  lines: number[];
  startColumn: number;
  endColumn: number;
  filename: string;
}

export interface CpgNode {
  id: string;
  type: CpgNodeType;
  name: string;
  sourceLocation?: SourceLocation;
  properties: Record<string, unknown>;
}

export interface ContractNode extends CpgNode {
  type: 'CONTRACT';
  properties: {
    inheritance: string[];
    libraries: string[];
    dependencies: string[];
    isAbstract?: boolean;
  };
}

export interface FunctionNode extends CpgNode {
  type: 'FUNCTION';
  properties: {
    signature: string;
    visibility: string;
    stateMutability: string;
    modifiers: string[];
    parameters: ParameterInfo[];
    returns: ParameterInfo[];
    isVirtual?: boolean;
    isOverride?: boolean;
    isPayable?: boolean;
    isReceive?: boolean;
    isFallback?: boolean;
  };
}

export interface VariableNode extends CpgNode {
  type: 'VARIABLE';
  properties: {
    variableType: string;
    visibility?: string;
    scope: VariableScope;
    isStateVariable: boolean;
    isConstant?: boolean;
    isImmutable?: boolean;
    isPayable?: boolean;
    mutability?: string;
    isTainted?: boolean;
    taintSource?: string;
    isTransient?: boolean;
  };
}

export interface ExpressionNode extends CpgNode {
  type: 'EXPRESSION';
  properties: {
    expressionType: string;
    operator?: string;
    value?: string;
    isTainted?: boolean;
    taintSources?: string[];
  };
}

export interface AssignmentNode extends CpgNode {
  type: 'ASSIGNMENT';
  properties: {
    operator: string;
    isSink?: boolean;
    sinkType?: SinkType;
    taintFlow?: TaintFlow;
  };
}

export type CpgNodeType =
  | 'CONTRACT'
  | 'FUNCTION'
  | 'VARIABLE'
  | 'EXPRESSION'
  | 'ASSIGNMENT'
  | 'MODIFIER'
  | 'EVENT'
  | 'STRUCT'
  | 'ENUM'
  | 'ENUM_VALUE'
  | 'LIBRARY'
  | 'INTERFACE'
  | 'ERROR'
  | 'USING_FOR'
  | 'CFG_BLOCK'
  | 'STATEMENT'
  | 'PRAGMA'
  | 'IMPORT'
  | 'ASSEMBLY_BLOCK'
  | 'YUL_BLOCK'
  | 'UNCHECKED_BLOCK'
  | 'TRY_CATCH_CLAUSE'
  | 'CONDITIONAL'
  | 'FUNCTION_CALL_OPTIONS'
  | 'TYPE_ALIAS'
  | 'LOW_LEVEL_CALL'
  | 'CREATE2'
  | 'PROXY_PATTERN'
  | 'RECEIVE_FUNCTION'
  | 'FALLBACK_FUNCTION'
  | 'ABSTRACT_CONTRACT'
  | 'TRANSIENT_STORAGE';

export type VariableScope = 'STATE' | 'LOCAL' | 'PARAMETER' | 'RETURN';

export type SinkType =
  | 'STATE_WRITE'
  | 'EXTERNAL_CALL'
  | 'STORAGE_WRITE'
  | 'EMIT_EVENT';

export interface ParameterInfo {
  name: string;
  type: string;
  indexed?: boolean;
}

export interface TaintFlow {
  source: string;
  sink: string;
  path: string[];
}

export interface CpgEdge {
  id: string;
  type: CpgEdgeType;
  source: string;
  target: string;
  properties: Record<string, unknown>;
}

export interface ContainsEdge extends CpgEdge {
  type: 'CONTAINS';
  properties: {
    order?: number;
  };
}

export interface CallsEdge extends CpgEdge {
  type: 'CALLS';
  properties: {
    callType: string;
  };
}

export interface ReadsEdge extends CpgEdge {
  type: 'READS';
  properties: {
    accessType: string;
  };
}

export interface WritesEdge extends CpgEdge {
  type: 'WRITES';
  properties: {
    writeType: string;
    isSink?: boolean;
  };
}

export interface TaintEdge extends CpgEdge {
  type: 'TAINT';
  properties: {
    taintType: string;
    confidence: number;
  };
}

export interface ControlFlowEdge extends CpgEdge {
  type: 'CONTROL_FLOW';
  properties: {
    flowType: string;
    condition?: string;
  };
}

export type CpgEdgeType =
  | 'CONTAINS'
  | 'CALLS'
  | 'READS'
  | 'WRITES'
  | 'TAINT'
  | 'CONTROL_FLOW'
  | 'DATA_FLOW'
  | 'CALL_GRAPH'
  | 'INHERITS'
  | 'IMPLEMENTS'
  | 'MODIFIES';

export interface CpgGraph {
  nodes: Map<string, CpgNode>;
  edges: Map<string, CpgEdge>;
  metadata: CpgMetadata;
}

export interface CpgMetadata {
  sourceFile: string;
  timestamp: number;
  slitherVersion?: string;
  nodeCount: number;
  edgeCount: number;
  contractCount: number;
  functionCount: number;
  variableCount: number;
  taintFlows: TaintFlow[];
  sinks: SinkInfo[];
}

export interface SinkInfo {
  nodeId: string;
  sinkType: SinkType;
  location: SourceLocation;
  taintSources: string[];
}

// Modern Solidity node types
export interface ModifierNode extends CpgNode {
  type: 'MODIFIER';
  properties: {
    parameters: ParameterInfo[];
    virtual: boolean;
    override: boolean;
  };
}

export interface EventNode extends CpgNode {
  type: 'EVENT';
  properties: {
    parameters: ParameterInfo[];
    anonymous: boolean;
  };
}

export interface StructNode extends CpgNode {
  type: 'STRUCT';
  properties: {
    members: ParameterInfo[];
  };
}

export interface EnumNode extends CpgNode {
  type: 'ENUM';
  properties: {
    values: string[];
  };
}

export interface LibraryNode extends CpgNode {
  type: 'LIBRARY';
  properties: {
    functions: string[];
    dependencies: string[];
  };
}

export interface InterfaceNode extends CpgNode {
  type: 'INTERFACE';
  properties: {
    functions: string[];
    inheritance: string[];
  };
}

export interface ErrorNode extends CpgNode {
  type: 'ERROR';
  properties: {
    parameters: ParameterInfo[];
  };
}

export interface UsingForNode extends CpgNode {
  type: 'USING_FOR';
  properties: {
    libraryName: string;
    typeName: string;
    functions: string[];
  };
}

export interface CfgBlockNode extends CpgNode {
  type: 'CFG_BLOCK';
  properties: {
    blockType: 'ENTRY' | 'EXIT' | 'BASIC' | 'CONDITIONAL' | 'LOOP';
    statements: string[];
  };
}

export interface StatementNode extends CpgNode {
  type: 'STATEMENT';
  properties: {
    statementType: string;
    expression?: string;
  };
}

// Enhanced edge types
export interface DataFlowEdge extends CpgEdge {
  type: 'DATA_FLOW';
  properties: {
    flowType: 'DEF' | 'USE' | 'DEF_USE';
    variable: string;
  };
}

export interface CallGraphEdge extends CpgEdge {
  type: 'CALL_GRAPH';
  properties: {
    callType: 'DIRECT' | 'INDIRECT' | 'EXTERNAL' | 'DELEGATE';
    gasLimit?: number;
    value?: string;
  };
}

export interface InheritsEdge extends CpgEdge {
  type: 'INHERITS';
  properties: {
    inheritanceType: 'CONTRACT' | 'INTERFACE';
  };
}

export interface ImplementsEdge extends CpgEdge {
  type: 'IMPLEMENTS';
  properties: {
    interfaceName: string;
  };
}

export interface ModifiesEdge extends CpgEdge {
  type: 'MODIFIES';
  properties: {
    modifierName: string;
  };
}

export interface TaintEdge extends CpgEdge {
  type: 'TAINT';
  properties: {
    taintType: string;
    confidence: number;
  };
}

// Additional node types for comprehensive language support
export interface PragmaNode extends CpgNode {
  type: 'PRAGMA';
  properties: {
    pragmaType: string;
    version?: string;
    features?: string[];
  };
}

export interface ImportNode extends CpgNode {
  type: 'IMPORT';
  properties: {
    importPath: string;
    symbols?: string[];
    alias?: string;
  };
}

export interface AssemblyBlockNode extends CpgNode {
  type: 'ASSEMBLY_BLOCK';
  properties: {
    dialect?: string;
    operations: string[];
  };
}

export interface LowLevelCallNode extends CpgNode {
  type: 'LOW_LEVEL_CALL';
  properties: {
    callType: 'call' | 'delegatecall' | 'staticcall';
    target: string;
    value?: string;
    gasLimit?: string;
  };
}

export interface Create2Node extends CpgNode {
  type: 'CREATE2';
  properties: {
    salt: string;
    bytecode: string;
    constructorArgs?: string[];
  };
}

export interface ProxyPatternNode extends CpgNode {
  type: 'PROXY_PATTERN';
  properties: {
    patternType: 'transparent' | 'uups' | 'beacon' | 'diamond';
    implementation?: string;
    admin?: string;
  };
}

// Phase 1: Critical Missing Features Node Types

export interface ReceiveFunctionNode extends CpgNode {
  type: 'RECEIVE_FUNCTION';
  properties: {
    visibility: 'external';
    stateMutability: 'payable';
    isReceive: true;
  };
}

export interface FallbackFunctionNode extends CpgNode {
  type: 'FALLBACK_FUNCTION';
  properties: {
    visibility: 'external';
    stateMutability: 'payable' | 'nonpayable';
    isFallback: true;
    hasParameters: boolean;
  };
}

export interface AbstractContractNode extends CpgNode {
  type: 'ABSTRACT_CONTRACT';
  properties: {
    isAbstract: true;
    abstractFunctions: string[];
    virtualFunctions: string[];
    inheritance: string[];
  };
}

export interface TransientStorageNode extends CpgNode {
  type: 'TRANSIENT_STORAGE';
  properties: {
    variableType: string;
    isTransient: true;
    scope: 'TRANSIENT';
    storageLocation: 'transient';
  };
}

// Utility types for analysis
export interface AnalysisContext {
  currentContract?: string;
  currentFunction?: string;
  variableScopes: Map<string, VariableScope>;
  taintedVariables: Set<string>;
  stateVariables: Set<string>;
}
