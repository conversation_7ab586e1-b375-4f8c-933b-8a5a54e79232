/**
 * Enhanced CPG Transformer for Solidity AST
 * Modular, extensible, and thoroughly tested transformer
 * Converts complete Solidity AST to comprehensive Code Property Graph
 */

import {
  SolidityAstResult,
  SolidityAstNode,
} from '../services/solidity-ast.service';
import {
  CpgGraph,
  CpgNode,
  CpgEdge,
  SourceLocation,
  CpgMetadata,
  AnalysisContext,
  TaintFlow,
  SinkInfo,
  SinkType,
} from '../types/cpg';

// Import processors for proper architecture
import { BaseProcessor, ProcessorContext } from './processors/base-processor';
import { ContractProcessor } from './processors/contract-processor';
import { FunctionProcessor } from './processors/function-processor';
import { VariableProcessor } from './processors/variable-processor';
import { ExpressionProcessor } from './processors/expression-processor';
import { PragmaProcessor } from './processors/pragma-processor';
import { ImportProcessor } from './processors/import-processor';
import { ModifierProcessor } from './processors/modifier-processor';
import { EventProcessor } from './processors/event-processor';
import { StructProcessor } from './processors/struct-processor';
import { EnumProcessor } from './processors/enum-processor';
import { ErrorProcessor } from './processors/error-processor';
import { AssemblyProcessor } from './processors/assembly-processor';
import { UsingForProcessor } from './processors/using-for-processor';
import { LibraryProcessor } from './processors/library-processor';
import { StatementProcessor } from './processors/statement-processor';
import { ParameterListProcessor } from './processors/parameter-list-processor';
import { ElementaryTypeProcessor } from './processors/elementary-type-processor';
import { DocumentationProcessor } from './processors/documentation-processor';
import { SourceUnitProcessor } from './processors/source-unit-processor';
import { ModifierInvocationProcessor } from './processors/modifier-invocation-processor';
import { IdentifierPathProcessor } from './processors/identifier-path-processor';
import { UserDefinedTypeProcessor } from './processors/user-defined-type-processor';
import { ArrayTypeProcessor } from './processors/array-type-processor';
import { MappingProcessor } from './processors/mapping-processor';
import { TupleExpressionProcessor } from './processors/tuple-expression-processor';
import { OverrideSpecifierProcessor } from './processors/override-specifier-processor';
import { InheritanceSpecifierProcessor } from './processors/inheritance-specifier-processor';
import { ElementaryTypeNameExpressionProcessor } from './processors/elementary-type-name-expression-processor';
import { NewExpressionProcessor } from './processors/new-expression-processor';
import { UserDefinedValueTypeProcessor } from './processors/user-defined-value-type-processor';
import { ConditionalProcessor } from './processors/conditional-processor';
import { YulBlockProcessor } from './processors/yul-block-processor';
import { UncheckedBlockProcessor } from './processors/unchecked-block-processor';
import { TryCatchClauseProcessor } from './processors/try-catch-clause-processor';
import { EnumValueProcessor } from './processors/enum-value-processor';
import { FunctionCallOptionsProcessor } from './processors/function-call-options-processor';
import { YulAssignmentProcessor } from './processors/yul-assignment-processor';
import { YulVariableDeclarationProcessor } from './processors/yul-variable-declaration-processor';
import { YulFunctionCallProcessor } from './processors/yul-function-call-processor';
import { YulIdentifierProcessor } from './processors/yul-identifier-processor';
import { YulLiteralProcessor } from './processors/yul-literal-processor';
import { YulTypedNameProcessor } from './processors/yul-typed-name-processor';
import { YulExpressionStatementProcessor } from './processors/yul-expression-statement-processor';
import { YulForLoopProcessor } from './processors/yul-for-loop-processor';
import { YulSwitchProcessor } from './processors/yul-switch-processor';
import { YulCaseProcessor } from './processors/yul-case-processor';
import { YulIfProcessor } from './processors/yul-if-processor';
import { YulFunctionDefinitionProcessor } from './processors/yul-function-definition-processor';

// Import analyzers for integration
import { TaintAnalyzer } from './analyzers/taint-analyzer';
import { CallGraphAnalyzer } from './analyzers/callgraph-analyzer';
import { DataFlowAnalyzer } from './analyzers/dataflow-analyzer';

export class SolidityCpgTransformer {
  private nodes: Map<string, CpgNode> = new Map();
  private edges: Map<string, CpgEdge> = new Map();
  private context: AnalysisContext = {
    variableScopes: new Map(),
    taintedVariables: new Set(),
    stateVariables: new Set(),
  };
  private nodeIdCounter = 0;
  private edgeIdCounter = 0;

  // Processor instances
  private processors: BaseProcessor[] = [];
  private processorContext!: ProcessorContext; // Will be initialized in constructor

  // Recursion protection
  private processingNodes = new Set<string>();

  constructor() {
    this.initializeProcessors();
  }

  /**
   * Initialize all processors with proper context
   */
  private initializeProcessors(): void {
    console.log('🔧 Initializing processors...');
    this.processorContext = {
      generateNodeId: () => this.generateNodeId(),
      generateEdgeId: () => this.generateEdgeId(),
      extractSourceLocation: (node, filePath) =>
        this.extractSourceLocation(node, filePath),
      getAnalysisContext: () => this.context,
      updateAnalysisContext: (context) => {
        this.context = { ...this.context, ...context };
      },
      addNode: (node) => this.nodes.set(node.id, node),
      addEdge: (edge) => this.edges.set(edge.id, edge),
      getNode: (id) => this.nodes.get(id),
    };

    // Initialize processors in priority order (highest priority first)
    this.processors = [
      new SourceUnitProcessor(this.processorContext), // 110
      new ContractProcessor(this.processorContext), // 100
      new LibraryProcessor(this.processorContext), // 95
      new PragmaProcessor(this.processorContext), // 90
      new ImportProcessor(this.processorContext), // 85
      new FunctionProcessor(this.processorContext), // 80
      new ModifierProcessor(this.processorContext), // 75
      new ModifierInvocationProcessor(this.processorContext), // 72
      new VariableProcessor(this.processorContext), // 70
      new UsingForProcessor(this.processorContext), // 68
      new EventProcessor(this.processorContext), // 65
      new StructProcessor(this.processorContext), // 60
      new ErrorProcessor(this.processorContext), // 58
      new EnumProcessor(this.processorContext), // 55
      new EnumValueProcessor(this.processorContext), // 54
      new TupleExpressionProcessor(this.processorContext), // 52
      new FunctionCallOptionsProcessor(this.processorContext), // 51
      new ExpressionProcessor(this.processorContext), // 50
      new ConditionalProcessor(this.processorContext), // 49
      new StatementProcessor(this.processorContext), // 48
      new UncheckedBlockProcessor(this.processorContext), // 47
      new TryCatchClauseProcessor(this.processorContext), // 46
      new NewExpressionProcessor(this.processorContext), // 45
      new AssemblyProcessor(this.processorContext), // 45
      new YulBlockProcessor(this.processorContext), // 44
      new YulAssignmentProcessor(this.processorContext), // 43
      new InheritanceSpecifierProcessor(this.processorContext), // 43
      new YulVariableDeclarationProcessor(this.processorContext), // 42
      new YulFunctionCallProcessor(this.processorContext), // 41
      new YulIdentifierProcessor(this.processorContext), // 40
      new YulLiteralProcessor(this.processorContext), // 39
      new YulTypedNameProcessor(this.processorContext), // 38
      new YulExpressionStatementProcessor(this.processorContext), // 37
      new YulForLoopProcessor(this.processorContext), // 36
      new YulSwitchProcessor(this.processorContext), // 35
      new YulCaseProcessor(this.processorContext), // 34
      new YulIfProcessor(this.processorContext), // 33
      new YulFunctionDefinitionProcessor(this.processorContext), // 32
      new IdentifierPathProcessor(this.processorContext), // 42
      new OverrideSpecifierProcessor(this.processorContext), // 41
      new ParameterListProcessor(this.processorContext), // 40
      new UserDefinedValueTypeProcessor(this.processorContext), // 39
      new UserDefinedTypeProcessor(this.processorContext), // 38
      new MappingProcessor(this.processorContext), // 37
      new ElementaryTypeNameExpressionProcessor(this.processorContext), // 36
      new ArrayTypeProcessor(this.processorContext), // 35
      new ElementaryTypeProcessor(this.processorContext), // 34
      new DocumentationProcessor(this.processorContext), // 30
    ];
    console.log(`✅ Initialized ${this.processors.length} processors`);
  }

  /**
   * Get the appropriate processor for a given AST node type
   */
  private getProcessorForNode(nodeType: string): BaseProcessor | null {
    return (
      this.processors.find((processor) => processor.canProcess(nodeType)) ||
      null
    );
  }

  /**
   * Transform Solidity AST to comprehensive CPG
   */
  public astToCpg(astResult: SolidityAstResult): CpgGraph {
    this.reset();

    if (!astResult.success || !astResult.ast) {
      throw new Error(
        `Invalid AST result: ${astResult.error || 'Unknown error'}`
      );
    }

    console.log('🔄 Transforming Solidity AST to CPG...');

    // Process the root AST node
    this.processAstNode(astResult.ast, astResult.filePath);

    // Perform advanced analysis
    this.performDataFlowAnalysis();
    this.performTaintAnalysis();
    this.buildCallGraph();

    const metadata = this.buildMetadata(
      astResult.filePath,
      astResult.compilerVersion
    );

    console.log(
      `✅ CPG transformation complete: ${this.nodes.size} nodes, ${this.edges.size} edges`
    );

    return {
      nodes: this.nodes,
      edges: this.edges,
      metadata,
    };
  }

  private reset(): void {
    this.nodes.clear();
    this.edges.clear();
    this.processingNodes.clear();
    this.context = {
      variableScopes: new Map(),
      taintedVariables: new Set(),
      stateVariables: new Set(),
    };
    this.nodeIdCounter = 0;
    this.edgeIdCounter = 0;
  }

  private generateNodeId(): string {
    return `node_${++this.nodeIdCounter}`;
  }

  private generateEdgeId(): string {
    return `edge_${++this.edgeIdCounter}`;
  }

  private processAstNode(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): string {
    // Create a unique key for this node to prevent infinite recursion
    const nodeKey = `${node.nodeType}_${node.src || 'no_src'}_${node.name || 'unnamed'}`;

    if (this.processingNodes.has(nodeKey)) {
      console.log(`🔄 Skipping already processing node: ${nodeKey}`);
      return this.generateNodeId(); // Return a dummy ID to prevent issues
    }

    this.processingNodes.add(nodeKey);

    try {
      // Try to find a processor for this node type
      const processor = this.getProcessorForNode(node.nodeType);

      if (processor) {
        console.log(
          `🔧 Using processor for ${node.nodeType}: ${node.name || 'unnamed'}`
        );
        // Use the specialized processor
        const result = processor.process(node, filePath, parentId);

        // Add the node and edges to our collections
        this.nodes.set(result.node.id, result.node);
        result.edges.forEach((edge: CpgEdge) => {
          this.edges.set(edge.id, edge);
        });

        // Process child nodes
        if (node.nodes) {
          for (const childNode of node.nodes) {
            this.processAstNode(childNode, filePath, result.node.id);
          }
        }

        // Process other child properties
        this.processChildProperties(node, filePath, result.node.id);

        return result.node.id;
      } else {
        console.log(
          `⚠️ Using legacy processing for ${node.nodeType}: ${node.name || 'unnamed'}`
        );
        // Fallback to legacy processing for unsupported node types
        return this.processAstNodeLegacy(node, filePath, parentId);
      }
    } finally {
      this.processingNodes.delete(nodeKey);
    }
  }

  /**
   * Legacy processing for node types not yet supported by processors
   * NOTE: This should NEVER be used as all node types must have processors
   */
  private processAstNodeLegacy(
    node: SolidityAstNode,
    _filePath: string,
    _parentId?: string
  ): string {
    // All node types should now be handled by processors
    // If we reach here, it means a processor is missing for this node type
    console.error(
      `❌ CRITICAL: No processor found for node type: ${node.nodeType}. This indicates a missing processor and violates our architecture.`
    );

    // Instead of creating nodes directly, we should throw an error
    // This forces us to create proper processors for all node types
    throw new Error(
      `Missing processor for node type: ${node.nodeType}. Please implement a processor for this node type.`
    );
  }

  private processChildProperties(
    node: SolidityAstNode,
    filePath: string,
    parentId: string
  ): void {
    // Process all properties that might contain child nodes
    const nodeAny = node as any;

    // First, process known important properties
    const priorityProperties = [
      'body',
      'parameters',
      'returnParameters',
      'modifiers',
      'baseContracts',
      'statements', // For function bodies and blocks
      'nodes', // For contract-level nodes
    ];

    for (const prop of priorityProperties) {
      const value = nodeAny[prop];
      if (value && typeof value === 'object') {
        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item && item.nodeType) {
              this.processAstNode(item, filePath, parentId);
            }
          });
        } else if (value.nodeType) {
          this.processAstNode(value, filePath, parentId);
        }
      }
    }

    // Then, recursively process all other properties that might contain nodes
    for (const [key, value] of Object.entries(nodeAny)) {
      // Skip already processed properties and non-object values
      if (
        priorityProperties.includes(key) ||
        !value ||
        typeof value !== 'object'
      ) {
        continue;
      }

      if (Array.isArray(value)) {
        value.forEach((item) => {
          if (item && typeof item === 'object' && item.nodeType) {
            this.processAstNode(item, filePath, parentId);
          }
        });
      } else if ((value as any).nodeType) {
        this.processAstNode(value as SolidityAstNode, filePath, parentId);
      }
    }
  }

  private performDataFlowAnalysis(): void {
    console.log('📊 Performing sophisticated data flow analysis...');

    // Create CPG graph object for analyzer
    const cpgGraph = {
      nodes: this.nodes,
      edges: this.edges,
      metadata: {} as any, // Will be populated
    };

    // Instantiate and run data flow analyzer
    const dataFlowAnalyzer = new DataFlowAnalyzer(cpgGraph, this.context);
    const dataFlowResult = dataFlowAnalyzer.analyze();

    // The analyzer already creates DATA_FLOW edges internally
    // We just need to log the results since the analyzer modifies the CPG directly

    console.log(
      `✅ Data flow analysis complete: ${dataFlowResult.defUseChains.size} def-use chains`
    );
  }

  private performTaintAnalysis(): void {
    console.log('🔍 Performing comprehensive taint analysis...');

    // Create CPG graph object for analyzer
    const cpgGraph = {
      nodes: this.nodes,
      edges: this.edges,
      metadata: {} as any, // Will be populated
    };

    // Instantiate and run taint analyzer
    const taintAnalyzer = new TaintAnalyzer(cpgGraph, this.context);
    const taintResult = taintAnalyzer.analyze();

    // Integrate results back into CPG
    taintResult.taintFlows.forEach((flow) => {
      // Ensure we have valid source and target
      const source = flow.path[0];
      const target = flow.path[flow.path.length - 1];

      if (flow.path.length >= 2 && source && target) {
        const taintEdge = {
          id: this.generateEdgeId(),
          type: 'TAINT' as const,
          source: source,
          target: target,
          properties: {
            taintType: 'data_flow',
            confidence: 1.0, // Use number instead of string
            flowPath: flow.path,
          },
        };
        this.edges.set(taintEdge.id, taintEdge);
      }
    });

    // Update context with tainted nodes
    this.context.taintedVariables = taintResult.taintedNodes;

    console.log(
      `✅ Taint analysis complete: ${taintResult.taintFlows.length} flows found`
    );
  }

  private buildCallGraph(): void {
    console.log('📞 Building comprehensive call graph...');

    // Create CPG graph object for analyzer
    const cpgGraph = {
      nodes: this.nodes,
      edges: this.edges,
      metadata: {} as any, // Will be populated
    };

    // Instantiate and run call graph analyzer
    const callGraphAnalyzer = new CallGraphAnalyzer(cpgGraph, this.context);
    const callGraphResult = callGraphAnalyzer.analyze();

    // The analyzer already creates call graph edges internally
    // We just need to log the results since the analyzer modifies the CPG directly

    console.log(
      `✅ Call graph complete: ${callGraphResult.callGraph.size} callers, ${callGraphResult.entryPoints.length} entry points`
    );
  }

  private extractSourceLocation(
    node: SolidityAstNode,
    filePath: string
  ): SourceLocation | undefined {
    if (!node.src) return undefined;

    const parts = node.src.split(':');
    if (parts.length < 2) return undefined;

    return {
      start: parseInt(parts[0] || '0', 10),
      length: parseInt(parts[1] || '0', 10),
      lines: [], // TODO: Calculate line numbers
      startColumn: 0, // TODO: Calculate column numbers
      endColumn: 0,
      filename: filePath,
    };
  }

  private buildMetadata(
    filePath: string,
    compilerVersion?: string
  ): CpgMetadata {
    const nodesByType = new Map<string, number>();
    for (const node of this.nodes.values()) {
      nodesByType.set(node.type, (nodesByType.get(node.type) || 0) + 1);
    }

    return {
      sourceFile: filePath,
      timestamp: Date.now(),
      ...(compilerVersion && { slitherVersion: compilerVersion }),
      nodeCount: this.nodes.size,
      edgeCount: this.edges.size,
      contractCount: nodesByType.get('CONTRACT') || 0,
      functionCount: nodesByType.get('FUNCTION') || 0,
      variableCount: nodesByType.get('VARIABLE') || 0,
      taintFlows: this.extractTaintFlowsFromEdges(),
      sinks: this.extractSinksFromNodes(),
    };
  }

  /**
   * Extract taint flows from TAINT edges in the CPG
   */
  private extractTaintFlowsFromEdges(): TaintFlow[] {
    const taintFlows: TaintFlow[] = [];

    for (const edge of this.edges.values()) {
      if (edge.type === 'TAINT') {
        const sourceNode = this.nodes.get(edge.source);
        const targetNode = this.nodes.get(edge.target);

        if (sourceNode && targetNode) {
          taintFlows.push({
            source: sourceNode.name,
            sink: targetNode.name,
            path: (edge.properties['flowPath'] as string[]) || [
              edge.source,
              edge.target,
            ],
          });
        }
      }
    }

    return taintFlows;
  }

  /**
   * Extract sinks from nodes in the CPG
   */
  private extractSinksFromNodes(): SinkInfo[] {
    const sinks: SinkInfo[] = [];

    for (const node of this.nodes.values()) {
      if (this.isSinkNode(node)) {
        const sinkInfo: SinkInfo = {
          nodeId: node.id,
          sinkType: this.determineSinkType(node),
          location: node.sourceLocation || {
            start: 0,
            length: 0,
            lines: [],
            startColumn: 0,
            endColumn: 0,
            filename: 'unknown',
          },
          taintSources: this.getTaintSourcesForNode(node.id),
        };
        sinks.push(sinkInfo);
      }
    }

    return sinks;
  }

  /**
   * Check if a node is a sink
   */
  private isSinkNode(node: CpgNode): boolean {
    // Assignment nodes with sink property
    if (node.type === 'ASSIGNMENT' && node.properties['isSink']) {
      return true;
    }

    // State variable writes
    if (node.type === 'VARIABLE' && node.properties['isStateVariable']) {
      return true;
    }

    // External function calls
    if (
      node.type === 'EXPRESSION' &&
      node.properties['expressionType'] === 'FunctionCall' &&
      node.properties['isExternal']
    ) {
      return true;
    }

    return false;
  }

  /**
   * Determine the sink type for a node
   */
  private determineSinkType(node: CpgNode): SinkType {
    if (node.type === 'ASSIGNMENT') {
      return (node.properties['sinkType'] as SinkType) || 'STATE_WRITE';
    }

    if (node.type === 'VARIABLE' && node.properties['isStateVariable']) {
      return 'STATE_WRITE';
    }

    if (node.type === 'EXPRESSION' && node.properties['isExternal']) {
      return 'EXTERNAL_CALL';
    }

    return 'STATE_WRITE';
  }

  /**
   * Get taint sources that flow to this node
   */
  private getTaintSourcesForNode(nodeId: string): string[] {
    const sources: string[] = [];

    for (const edge of this.edges.values()) {
      if (edge.type === 'TAINT' && edge.target === nodeId) {
        const sourceNode = this.nodes.get(edge.source);
        if (sourceNode) {
          sources.push(sourceNode.name);
        }
      }
    }

    return sources;
  }
}

/**
 * Main export function for transforming Solidity AST to CPG
 */
export function solidityAstToCpg(astResult: SolidityAstResult): CpgGraph {
  const transformer = new SolidityCpgTransformer();
  return transformer.astToCpg(astResult);
}
