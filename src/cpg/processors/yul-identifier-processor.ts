/**
 * Yul Identifier Processor
 * Handles YulIdentifier nodes (identifiers in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulIdentifierProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 40; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulIdentifier';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract identifier information
    const identifierInfo = this.extractIdentifierInfo(node);

    // Create the Yul identifier node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: identifierInfo.name || 'unnamed_yul_identifier',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulIdentifier: true,
        identifierName: identifierInfo.name,
        isBuiltinFunction: identifierInfo.isBuiltinFunction,
        isVariable: identifierInfo.isVariable,
        scope: 'yul',
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_identifier',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // No child nodes for identifiers
    };
  }

  private extractIdentifierInfo(node: SolidityAstNode): {
    name: string;
    isBuiltinFunction: boolean;
    isVariable: boolean;
  } {
    const nodeAny = node as any;
    const name = nodeAny.name || 'unnamed';
    
    return {
      name,
      isBuiltinFunction: this.isBuiltinFunction(name),
      isVariable: !this.isBuiltinFunction(name),
    };
  }

  private isBuiltinFunction(name: string): boolean {
    const builtinFunctions = [
      // Arithmetic
      'add', 'sub', 'mul', 'div', 'mod', 'exp',
      // Comparison
      'lt', 'gt', 'eq', 'iszero',
      // Bitwise
      'and', 'or', 'xor', 'not', 'shl', 'shr', 'sar',
      // Memory
      'mload', 'mstore', 'mstore8', 'msize',
      // Storage
      'sload', 'sstore',
      // Crypto
      'keccak256', 'sha256', 'ripemd160', 'ecrecover',
      // Block/Transaction
      'blockhash', 'coinbase', 'timestamp', 'number', 'difficulty', 'gaslimit',
      'chainid', 'selfbalance', 'balance', 'caller', 'callvalue', 'calldataload',
      'calldatasize', 'calldatacopy', 'codesize', 'codecopy', 'gasprice', 'extcodesize',
      'extcodecopy', 'returndatasize', 'returndatacopy',
      // Control flow
      'stop', 'return', 'revert', 'invalid',
      // Calls
      'call', 'callcode', 'delegatecall', 'staticcall',
      // Create
      'create', 'create2',
      // Logs
      'log0', 'log1', 'log2', 'log3', 'log4',
    ];
    
    return builtinFunctions.includes(name);
  }
}
