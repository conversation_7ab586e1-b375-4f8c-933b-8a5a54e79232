/**
 * Yul Typed Name Processor
 * Handles YulTypedName nodes (typed variable names in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulTypedNameProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 38; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulTypedName';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract typed name information
    const typedNameInfo = this.extractTypedNameInfo(node);

    // Create the Yul typed name node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'VARIABLE',
      name: typedNameInfo.name || 'unnamed_yul_typed_name',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulTypedName: true,
        variableName: typedNameInfo.name,
        dataType: typedNameInfo.type,
        scope: 'yul',
        isParameter: true, // YulTypedName is typically used for function parameters
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_typed_name',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // No child nodes for typed names
    };
  }

  private extractTypedNameInfo(node: SolidityAstNode): {
    name: string;
    type: string;
  } {
    const nodeAny = node as any;
    
    return {
      name: nodeAny.name || 'unnamed',
      type: nodeAny.type || 'uint256', // Default Yul type
    };
  }
}
