/**
 * Import Processor
 * Handles ImportDirective AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ImportNode } from '../../types/cpg';

export class ImportProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ImportDirective';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 85; // High priority for import directives
  }

  /**
   * Process ImportDirective AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract import information
    const importInfo = this.extractImportInfo(astNode);

    // Create import node
    const importNode: ImportNode = {
      id: nodeId,
      type: 'IMPORT',
      name: `import_${importInfo.sourceName}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        importPath: importInfo.sourceName,
        symbols: importInfo.symbols,
        ...(importInfo.alias && { alias: importInfo.alias }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (SourceUnit)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: importNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract import information from AST node
   */
  private extractImportInfo(astNode: SolidityAstNode): {
    sourceName: string;
    symbols: string[];
    isWildcard: boolean;
    alias?: string;
  } {
    const nodeAny = astNode as any;

    // Extract source file name
    const sourceName = nodeAny.file || nodeAny.absolutePath || 'unknown';

    // Extract imported symbols
    const symbols: string[] = [];
    if (nodeAny.symbolAliases && Array.isArray(nodeAny.symbolAliases)) {
      nodeAny.symbolAliases.forEach((symbolAlias: any) => {
        if (symbolAlias.foreign?.name) {
          symbols.push(symbolAlias.foreign.name);
        }
      });
    }

    // Check if it's a wildcard import (import * as ...)
    const isWildcard = symbols.length === 0 && !nodeAny.unitAlias;

    // Extract alias for the entire import
    const alias = nodeAny.unitAlias;

    return {
      sourceName,
      symbols,
      isWildcard,
      alias,
    };
  }
}
