/**
 * Yul Block Processor
 * Handles YulBlock nodes (inline assembly blocks)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulBlockProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 44; // Priority close to AssemblyProcessor
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulBlock';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Create the Yul block node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'YUL_BLOCK',
      name: 'yul_assembly_block',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isAssembly: true,
        isInline: true,
        statementCount: (node as any).statements?.length || 0,
        hasMemoryAccess: this.hasMemoryAccess(node),
        hasStorageAccess: this.hasStorageAccess(node),
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_block',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child statements will be processed separately
    };
  }

  private hasMemoryAccess(node: SolidityAstNode): boolean {
    // Check if the Yul block contains memory operations
    const statements = (node as any).statements || [];
    return statements.some((stmt: any) => this.containsMemoryOperations(stmt));
  }

  private hasStorageAccess(node: SolidityAstNode): boolean {
    // Check if the Yul block contains storage operations
    const statements = (node as any).statements || [];
    return statements.some((stmt: any) => this.containsStorageOperations(stmt));
  }

  private containsMemoryOperations(stmt: any): boolean {
    // Check for memory-related Yul operations
    const memoryOps = ['mload', 'mstore', 'mstore8', 'msize'];
    return this.containsOperations(stmt, memoryOps);
  }

  private containsStorageOperations(stmt: any): boolean {
    // Check for storage-related Yul operations
    const storageOps = ['sload', 'sstore'];
    return this.containsOperations(stmt, storageOps);
  }

  private containsOperations(stmt: any, operations: string[]): boolean {
    if (!stmt) return false;

    // Check if this statement is one of the operations
    if (stmt.name && operations.includes(stmt.name)) {
      return true;
    }

    // Recursively check nested statements
    if (stmt.body && Array.isArray(stmt.body)) {
      return stmt.body.some((nestedStmt: any) =>
        this.containsOperations(nestedStmt, operations)
      );
    }

    return false;
  }
}
