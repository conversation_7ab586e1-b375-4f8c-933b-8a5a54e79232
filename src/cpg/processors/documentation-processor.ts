/**
 * Documentation Processor
 * Handles StructuredDocumentation AST nodes (NatS<PERSON> comments)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class DocumentationProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'StructuredDocumentation';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 30; // Lowest priority - documentation node
  }

  /**
   * Process StructuredDocumentation AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract documentation content
    const documentation = this.extractDocumentation(astNode);

    // Create documentation node as an expression
    const docNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: 'documentation',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'StructuredDocumentation',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          content: documentation.content,
          tags: documentation.tags,
          isDocumentation: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: docNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract documentation content and tags from AST node
   */
  private extractDocumentation(astNode: SolidityAstNode): {
    content: string;
    tags: string[];
  } {
    const nodeAny = astNode as any;
    const text = nodeAny.text || '';

    // Extract NatSpec tags (@param, @return, @dev, etc.)
    const tags: string[] = [];
    const tagRegex = /@(\w+)/g;
    let match;
    while ((match = tagRegex.exec(text)) !== null) {
      const tag = match[1];
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    }

    return {
      content: text,
      tags,
    };
  }
}
