/**
 * Elementary Type Name Expression Processor
 * Handles ElementaryTypeNameExpression AST nodes (type expressions like address(0), uint256(123))
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class ElementaryTypeNameExpressionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ElementaryTypeNameExpression';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 36; // Medium priority - type expression node
  }

  /**
   * Process ElementaryTypeNameExpression AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract type expression information
    const typeInfo = this.extractTypeExpressionInfo(astNode);

    // Create elementary type name expression node
    const typeExpressionNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: typeInfo.expressionName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'ElementaryTypeNameExpression',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          typeName: typeInfo.typeName,
          isTypeExpression: true,
          typeCategory: typeInfo.typeCategory,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing (the type name)
    if (astNode['typeName']) {
      childNodeIds.push(`${nodeId}_typeName`);
    }

    // Update analysis context for type tracking
    this.updateTypeAnalysis(nodeId, typeInfo);

    const result: ProcessorResult = {
      node: typeExpressionNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract type expression information from AST node
   */
  private extractTypeExpressionInfo(astNode: SolidityAstNode): {
    expressionName: string;
    typeName: string;
    typeCategory: string;
  } {
    const nodeAny = astNode as any;
    
    // Extract type name
    let typeName = 'unknown';
    let typeCategory = 'elementary';
    
    if (nodeAny.typeName) {
      if (nodeAny.typeName.name) {
        typeName = nodeAny.typeName.name;
      } else if (nodeAny.typeName.typeDescriptions?.typeString) {
        typeName = nodeAny.typeName.typeDescriptions.typeString;
      }
      
      // Determine type category
      if (nodeAny.typeName.nodeType === 'ElementaryTypeName') {
        typeCategory = 'elementary';
      } else if (nodeAny.typeName.nodeType === 'UserDefinedTypeName') {
        typeCategory = 'userDefined';
      }
    }
    
    // Generate descriptive name
    const expressionName = `type_${typeName}`;

    return {
      expressionName,
      typeName,
      typeCategory,
    };
  }

  /**
   * Update analysis context for type tracking
   */
  private updateTypeAnalysis(nodeId: string, typeInfo: any): void {
    const context = this.context.getAnalysisContext();
    
    // Track type expressions for type analysis
    if (!(context as any).typeExpressions) {
      (context as any).typeExpressions = new Map();
    }
    
    (context as any).typeExpressions.set(nodeId, {
      typeName: typeInfo.typeName,
      typeCategory: typeInfo.typeCategory,
      isTypeExpression: true,
    });
  }
}
