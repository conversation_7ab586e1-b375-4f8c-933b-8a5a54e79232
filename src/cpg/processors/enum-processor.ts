/**
 * Enum Processor
 * Handles EnumDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { EnumNode } from '../../types/cpg';

export class EnumProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'EnumDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 55; // Medium priority for enums
  }

  /**
   * Process EnumDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract enum values
    const values = this.extractEnumValues(astNode);

    // Create enum node
    const enumNode: EnumNode = {
      id: nodeId,
      type: 'ENUM',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        values,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentEnum is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    const result: ProcessorResult = {
      node: enumNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract enum values from AST node
   */
  private extractEnumValues(astNode: SolidityAstNode): string[] {
    if (!astNode['members'] || !Array.isArray(astNode['members'])) {
      return [];
    }

    return astNode['members'].map((member: any) => member.name || 'unknown');
  }
}
