/**
 * Modifier Processor
 * Handles ModifierDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ModifierNode } from '../../types/cpg';

export class ModifierProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ModifierDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 75; // Medium-high priority for modifiers
  }

  /**
   * Process ModifierDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract modifier information
    const parameters = this.extractParameters(astNode.parameters);
    const isVirtual = this.isVirtualModifier(astNode);
    const isOverride = this.isOverrideModifier(astNode);

    // Create modifier node
    const modifierNode: ModifierNode = {
      id: nodeId,
      type: 'MODIFIER',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters,
        virtual: isVirtual,
        override: isOverride,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing
    this.markChildNodesForProcessing(astNode, nodeId, childNodeIds);

    const result: ProcessorResult = {
      node: modifierNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Check if modifier is virtual
   */
  private isVirtualModifier(astNode: SolidityAstNode): boolean {
    return Boolean(astNode['virtual'] === true);
  }

  /**
   * Check if modifier is an override
   */
  private isOverrideModifier(astNode: SolidityAstNode): boolean {
    return Boolean(
      astNode['override'] === true ||
        astNode['overrides'] ||
        astNode['baseFunctions']
    );
  }

  /**
   * Mark child nodes for processing
   */
  private markChildNodesForProcessing(
    astNode: SolidityAstNode,
    parentId: string,
    childNodeIds: string[]
  ): void {
    // Process parameters
    if (astNode.parameters?.parameters) {
      astNode.parameters.parameters['forEach']((_param: any, index: number) => {
        childNodeIds.push(`${parentId}_param_${index}`);
      });
    }

    // Process modifier body
    if (astNode.body) {
      childNodeIds.push(`${parentId}_body`);
    }
  }
}
