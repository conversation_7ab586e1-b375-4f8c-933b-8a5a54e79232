/**
 * Slither Expression Processor
 * Handles expression elements from Slither detector output
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { ExpressionNode } from '../../types/cpg';

export class SlitherExpressionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given element type
   */
  canProcess(elementType: string): boolean {
    // Handle any element type that's not specifically handled by other processors
    return !['contract', 'function', 'variable', 'pragma'].includes(
      elementType
    );
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 50; // Lower priority - fallback processor
  }

  /**
   * Process AST node (required by BaseProcessor)
   */
  process(
    _astNode: any,
    _filePath: string,
    _parentId?: string
  ): ProcessorResult {
    // This is not used for Slither processors, but required by interface
    throw new Error('Use processSlitherElement instead');
  }

  /**
   * Process Slither expression element
   */
  processSlitherElement(element: any, filePath: string): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.extractSlitherSourceLocation(
      element.source_mapping,
      filePath
    );

    const expressionNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: element.name || 'expression',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: element.type || 'unknown',
        isTainted: false,
        taintSources: [],
      },
    };

    const result: ProcessorResult = {
      node: expressionNode,
      edges: [],
      childNodeIds: [],
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract source location from Slither source mapping
   */
  private extractSlitherSourceLocation(
    sourceMapping: any,
    filePath: string
  ): any {
    if (!sourceMapping) return undefined;

    return {
      start: sourceMapping.start || 0,
      length: sourceMapping.length || 0,
      lines: sourceMapping.lines || [],
      startColumn: sourceMapping.starting_column || 0,
      endColumn: sourceMapping.ending_column || 0,
      filename: sourceMapping.filename_short || filePath,
    };
  }
}
