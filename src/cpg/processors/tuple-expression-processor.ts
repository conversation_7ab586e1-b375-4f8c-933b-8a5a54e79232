/**
 * Tuple Expression Processor
 * Handles TupleExpression AST nodes (tuple expressions like (a, b, c))
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class TupleExpressionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'TupleExpression';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 52; // Slightly higher than ExpressionProcessor
  }

  /**
   * Process TupleExpression AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract tuple information
    const tupleInfo = this.extractTupleInfo(astNode);

    // Create tuple expression node
    const tupleExpressionNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: tupleInfo.tupleName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'TupleExpression',
        isTainted: this.checkTaintStatus(astNode),
        taintSources: this.extractTaintSources(astNode),
        // Store additional data as value
        value: JSON.stringify({
          componentCount: tupleInfo.componentCount,
          hasNullComponents: tupleInfo.hasNullComponents,
          isInlineArray: tupleInfo.isInlineArray,
          isTupleExpression: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child component nodes for processing
    if (astNode['components']) {
      astNode['components']['forEach']((_component: any, index: number) => {
        // Some components might be null in tuples like (a, , c)
        if (_component !== null) {
          childNodeIds.push(`${nodeId}_component_${index}`);
        }
      });
    }

    // Update analysis context for taint tracking
    this.updateTaintAnalysis(nodeId, tupleInfo);

    const result: ProcessorResult = {
      node: tupleExpressionNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract tuple information from AST node
   */
  private extractTupleInfo(astNode: SolidityAstNode): {
    tupleName: string;
    componentCount: number;
    hasNullComponents: boolean;
    isInlineArray: boolean;
  } {
    const nodeAny = astNode as any;

    // Count components and check for nulls
    const components = nodeAny.components || [];
    const componentCount = components.length;
    const hasNullComponents = components.some((comp: any) => comp === null);

    // Check if this is an inline array (all components present, no nulls)
    const isInlineArray =
      nodeAny.isInlineArray || (!hasNullComponents && componentCount > 0);

    // Generate a descriptive name
    let tupleName = 'tuple';
    if (isInlineArray) {
      tupleName = `inline_array_${componentCount}`;
    } else if (hasNullComponents) {
      tupleName = `tuple_with_gaps_${componentCount}`;
    } else {
      tupleName = `tuple_${componentCount}`;
    }

    return {
      tupleName,
      componentCount,
      hasNullComponents,
      isInlineArray,
    };
  }

  /**
   * Check if the tuple expression is tainted
   */
  private checkTaintStatus(_astNode: SolidityAstNode): boolean {
    // A tuple is tainted if any of its non-null components are tainted
    // This is a simplified check - in practice, we'd need to analyze each component
    return false; // Will be updated during taint analysis phase
  }

  /**
   * Extract taint sources from tuple components
   */
  private extractTaintSources(_astNode: SolidityAstNode): string[] {
    // This will be populated during taint analysis
    return [];
  }

  /**
   * Update taint analysis context for tuple expressions
   */
  private updateTaintAnalysis(nodeId: string, _tupleInfo: any): void {
    const context = this.context.getAnalysisContext();

    // Track tuple expressions for later taint analysis
    if (!(context as any).tupleExpressions) {
      (context as any).tupleExpressions = new Set();
    }
    (context as any).tupleExpressions.add(nodeId);
  }
}
