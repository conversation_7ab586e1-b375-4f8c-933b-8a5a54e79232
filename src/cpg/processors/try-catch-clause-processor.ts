/**
 * Try Catch Clause Processor
 * Handles TryCatchClause nodes (catch clauses in try-catch statements)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class TryCatchClauseProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 46; // Priority between Statement and Expression
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'TryCatchClause';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Determine the clause type
    const clauseType = this.determineClauseType(node);

    // Create the try-catch clause node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'TRY_CATCH_CLAUSE',
      name: `${clauseType}_clause`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        clauseType,
        errorName: (node as any).errorName || null,
        hasParameters: !!(node as any).parameters,
        parameterCount: (node as any).parameters?.parameters?.length || 0,
        hasBlock: !!(node as any).block,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'catch_clause',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private determineClauseType(node: SolidityAstNode): string {
    const nodeAny = node as any;

    // Check if it's a specific error catch
    if (nodeAny.errorName) {
      return 'specific_error';
    }

    // Check if it has parameters (catch with error data)
    if (nodeAny.parameters && nodeAny.parameters.parameters?.length > 0) {
      return 'error_with_data';
    }

    // Default catch clause
    return 'default_catch';
  }
}
