/**
 * Unchecked Block Processor
 * Handles UncheckedBlock nodes (unchecked arithmetic blocks)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class UncheckedBlockProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 47; // Priority between Statement and Expression
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'UncheckedBlock';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Create the unchecked block node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'UNCHECKED_BLOCK',
      name: 'unchecked_block',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isUnchecked: true,
        statementCount: (node as any).statements?.length || 0,
        hasArithmetic: this.hasArithmeticOperations(node),
        riskLevel: this.calculateRiskLevel(node),
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'unchecked_block',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child statements will be processed separately
    };
  }

  private hasArithmeticOperations(node: SolidityAstNode): boolean {
    // Check if the unchecked block contains arithmetic operations
    const statements = (node as any).statements || [];
    return statements.some((stmt: any) => this.containsArithmetic(stmt));
  }

  private containsArithmetic(stmt: any): boolean {
    if (!stmt) return false;

    // Check for binary operations with arithmetic operators
    if (stmt.nodeType === 'BinaryOperation') {
      const arithmeticOps = ['+', '-', '*', '/', '%', '**'];
      return arithmeticOps.includes(stmt.operator);
    }

    // Check for unary operations
    if (stmt.nodeType === 'UnaryOperation') {
      const arithmeticOps = ['++', '--'];
      return arithmeticOps.includes(stmt.operator);
    }

    // Recursively check nested statements and expressions
    if (stmt.statements && Array.isArray(stmt.statements)) {
      return stmt.statements.some((nestedStmt: any) =>
        this.containsArithmetic(nestedStmt)
      );
    }

    // Check expression properties
    const expressionProps = ['left', 'right', 'expression', 'subExpression'];
    for (const prop of expressionProps) {
      if (stmt[prop] && this.containsArithmetic(stmt[prop])) {
        return true;
      }
    }

    return false;
  }

  private calculateRiskLevel(node: SolidityAstNode): 'LOW' | 'MEDIUM' | 'HIGH' {
    const statements = (node as any).statements || [];

    // High risk if many arithmetic operations
    if (statements.length > 5) return 'HIGH';

    // Medium risk if some arithmetic operations
    if (this.hasArithmeticOperations(node)) return 'MEDIUM';

    // Low risk otherwise
    return 'LOW';
  }
}
