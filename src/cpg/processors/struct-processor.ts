/**
 * Struct Processor
 * Handles StructDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { StructNode } from '../../types/cpg';

export class StructProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'StructDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 60; // Medium priority for structs
  }

  /**
   * Process StructDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract struct information
    const members = this.extractStructMembers(astNode);

    // Create struct node
    const structNode: StructNode = {
      id: nodeId,
      type: 'STRUCT',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        members,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark struct members for processing
    if (astNode['members']) {
      astNode['members']['forEach']((_member: any, index: number) => {
        childNodeIds.push(`${nodeId}_member_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: structNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract struct member information
   */
  private extractStructMembers(astNode: SolidityAstNode): Array<{
    name: string;
    type: string;
  }> {
    if (!astNode['members'] || !Array.isArray(astNode['members'])) {
      return [];
    }

    return astNode['members'].map((member: any) => ({
      name: member.name || 'unknown',
      type: member.typeDescriptions?.typeString || 'unknown',
    }));
  }
}
