/**
 * Yul For Loop Processor
 * Handles YulForLoop nodes (for loops in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulForLoopProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 36; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulForLoop';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract for loop information
    const loopInfo = this.extractLoopInfo(node);

    // Create the Yul for loop node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: 'yul_for_loop',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulForLoop: true,
        hasInitialization: loopInfo.hasInitialization,
        hasCondition: loopInfo.hasCondition,
        hasPost: loopInfo.hasPost,
        hasBody: loopInfo.hasBody,
        isInfiniteLoop: loopInfo.isInfiniteLoop,
        complexity: loopInfo.complexity,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_for_loop',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractLoopInfo(node: SolidityAstNode): {
    hasInitialization: boolean;
    hasCondition: boolean;
    hasPost: boolean;
    hasBody: boolean;
    isInfiniteLoop: boolean;
    complexity: 'LOW' | 'MEDIUM' | 'HIGH';
  } {
    const nodeAny = node as any;
    
    const hasInitialization = !!nodeAny.pre;
    const hasCondition = !!nodeAny.condition;
    const hasPost = !!nodeAny.post;
    const hasBody = !!nodeAny.body;
    
    // Check if it's an infinite loop (no condition or condition is always true)
    const isInfiniteLoop = !hasCondition || this.isAlwaysTrue(nodeAny.condition);
    
    return {
      hasInitialization,
      hasCondition,
      hasPost,
      hasBody,
      isInfiniteLoop,
      complexity: this.calculateComplexity(nodeAny),
    };
  }

  private isAlwaysTrue(condition: any): boolean {
    if (!condition) return false;
    
    // Check for literal true values
    if (condition.nodeType === 'YulLiteral') {
      return condition.value === '1' || condition.value === 'true';
    }
    
    return false;
  }

  private calculateComplexity(node: any): 'LOW' | 'MEDIUM' | 'HIGH' {
    // Simple heuristic based on loop structure
    let complexity = 0;
    
    if (node.pre) complexity += 1;
    if (node.condition) complexity += 1;
    if (node.post) complexity += 1;
    if (node.body) {
      // Check body complexity
      const bodyStatements = node.body.statements || [];
      complexity += bodyStatements.length;
    }
    
    if (complexity <= 2) return 'LOW';
    if (complexity <= 5) return 'MEDIUM';
    return 'HIGH';
  }
}
