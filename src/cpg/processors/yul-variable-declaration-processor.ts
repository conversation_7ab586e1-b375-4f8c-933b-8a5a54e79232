/**
 * Yul Variable Declaration Processor
 * Handles YulVariableDeclaration nodes (variable declarations in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulVariableDeclarationProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 42; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulVariableDeclaration';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract variable information
    const variableInfo = this.extractVariableInfo(node);

    // Create the Yul variable declaration node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'VARIABLE',
      name: variableInfo.name || 'unnamed_yul_var',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulVariable: true,
        scope: 'yul_local',
        variableCount: variableInfo.variableCount,
        hasInitialValue: variableInfo.hasInitialValue,
        isTemporary: true, // Yul variables are typically temporary
        dataType: 'uint256', // Yul variables are typically uint256
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_variable_declaration',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractVariableInfo(node: SolidityAstNode): {
    name: string;
    variableCount: number;
    hasInitialValue: boolean;
  } {
    const nodeAny = node as any;
    const variables = nodeAny.variables || [];
    
    // Get the first variable name if available
    const firstName = variables.length > 0 ? variables[0].name : 'unnamed_yul_var';
    
    return {
      name: firstName,
      variableCount: variables.length,
      hasInitialValue: !!nodeAny.value,
    };
  }
}
