/**
 * Yul Case Processor
 * Handles YulCase nodes (case statements in <PERSON><PERSON> switch)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulCaseProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 34; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulCase';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract case information
    const caseInfo = this.extractCaseInfo(node);

    // Create the Yul case node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: caseInfo.isDefaultCase ? 'yul_default_case' : `yul_case_${caseInfo.value}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulCase: true,
        caseValue: caseInfo.value,
        isDefaultCase: caseInfo.isDefaultCase,
        hasBody: caseInfo.hasBody,
        statementCount: caseInfo.statementCount,
        isLiteralCase: caseInfo.isLiteralCase,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_case',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractCaseInfo(node: SolidityAstNode): {
    value: string;
    isDefaultCase: boolean;
    hasBody: boolean;
    statementCount: number;
    isLiteralCase: boolean;
  } {
    const nodeAny = node as any;
    
    // Check if this is a default case
    const isDefaultCase = nodeAny.value === 'default' || !nodeAny.value;
    
    // Extract case value
    let value = 'default';
    if (!isDefaultCase && nodeAny.value) {
      if (typeof nodeAny.value === 'string') {
        value = nodeAny.value;
      } else if (nodeAny.value.value) {
        value = nodeAny.value.value;
      } else {
        value = 'unknown';
      }
    }
    
    // Check body
    const body = nodeAny.body;
    const hasBody = !!body;
    const statementCount = body?.statements?.length || 0;
    
    return {
      value,
      isDefaultCase,
      hasBody,
      statementCount,
      isLiteralCase: !isDefaultCase && this.isLiteralValue(nodeAny.value),
    };
  }

  private isLiteralValue(value: any): boolean {
    if (!value) return false;
    
    // Check if it's a YulLiteral
    if (value.nodeType === 'YulLiteral') {
      return true;
    }
    
    // Check if it's a simple string/number
    if (typeof value === 'string' || typeof value === 'number') {
      return true;
    }
    
    return false;
  }
}
