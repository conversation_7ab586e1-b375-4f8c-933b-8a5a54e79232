/**
 * Slither Contract Processor
 * Handles contract elements from Slither detector output
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { ContractNode } from '../../types/cpg';

export class SlitherContractProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given element type
   */
  canProcess(elementType: string): boolean {
    return elementType === 'contract';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 100; // Highest priority for contracts
  }

  /**
   * Process AST node (required by BaseProcessor)
   */
  process(
    _astNode: any,
    _filePath: string,
    _parentId?: string
  ): ProcessorResult {
    // This is not used for Slither processors, but required by interface
    throw new Error('Use processSlitherElement instead');
  }

  /**
   * Process Slither contract element
   */
  processSlitherElement(
    element: any,
    filePath: string,
    contractMap: Map<string, string>
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.extractSlitherSourceLocation(
      element.source_mapping,
      filePath
    );

    const parent = element.type_specific_fields?.parent;

    const contractNode: ContractNode = {
      id: nodeId,
      type: 'CONTRACT',
      name: parent?.name || element.name || 'Unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        inheritance: [],
        libraries: [],
        dependencies: [],
      },
    };

    // Update contract map for reference by other elements
    contractMap.set(contractNode.name, nodeId);

    // Update analysis context
    this.context.updateAnalysisContext({
      currentContract: contractNode.name,
    });

    const result: ProcessorResult = {
      node: contractNode,
      edges: [],
      childNodeIds: [],
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract source location from Slither source mapping
   */
  private extractSlitherSourceLocation(
    sourceMapping: any,
    filePath: string
  ): any {
    if (!sourceMapping) return undefined;

    return {
      start: sourceMapping.start || 0,
      length: sourceMapping.length || 0,
      lines: sourceMapping.lines || [],
      startColumn: sourceMapping.starting_column || 0,
      endColumn: sourceMapping.ending_column || 0,
      filename: sourceMapping.filename_short || filePath,
    };
  }
}
