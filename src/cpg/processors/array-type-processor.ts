/**
 * Array Type Processor
 * Handles ArrayTypeName AST nodes (array type definitions like uint256[], string[10])
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class ArrayTypeProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ArrayTypeName';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 36; // Medium priority - type information node
  }

  /**
   * Process ArrayTypeName AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract array type information
    const arrayInfo = this.extractArrayInfo(astNode);

    // Create array type node as an expression
    const arrayTypeNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: arrayInfo.fullTypeName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'ArrayTypeName',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          fullTypeName: arrayInfo.fullTypeName,
          baseTypeName: arrayInfo.baseTypeName,
          isFixedSize: arrayInfo.isFixedSize,
          arraySize: arrayInfo.arraySize,
          dimensions: arrayInfo.dimensions,
          isArrayType: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing
    if (astNode['baseType']) {
      childNodeIds.push(`${nodeId}_baseType`);
    }
    if (astNode['length']) {
      childNodeIds.push(`${nodeId}_length`);
    }

    const result: ProcessorResult = {
      node: arrayTypeNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract array type information from AST node
   */
  private extractArrayInfo(astNode: SolidityAstNode): {
    fullTypeName: string;
    baseTypeName: string;
    isFixedSize: boolean;
    arraySize?: number;
    dimensions: number;
  } {
    const nodeAny = astNode as any;

    // Extract base type name
    const baseTypeName =
      nodeAny.baseType?.name ||
      nodeAny.baseType?.typeDescriptions?.typeString ||
      'unknown';

    // Check if it's a fixed-size array
    const isFixedSize = nodeAny.length !== null && nodeAny.length !== undefined;

    // Extract array size if fixed
    let arraySize: number | undefined;
    if (isFixedSize && nodeAny.length) {
      if (typeof nodeAny.length === 'number') {
        arraySize = nodeAny.length;
      } else if (nodeAny.length.value) {
        arraySize = parseInt(nodeAny.length.value, 10);
      }
    }

    // Build full type name
    let fullTypeName = baseTypeName;
    if (isFixedSize && arraySize !== undefined) {
      fullTypeName += `[${arraySize}]`;
    } else {
      fullTypeName += '[]';
    }

    // For now, assume single dimension (can be extended for multi-dimensional arrays)
    const dimensions = 1;

    const result: {
      fullTypeName: string;
      baseTypeName: string;
      isFixedSize: boolean;
      arraySize?: number;
      dimensions: number;
    } = {
      fullTypeName,
      baseTypeName,
      isFixedSize,
      dimensions,
    };

    if (arraySize !== undefined) {
      result.arraySize = arraySize;
    }

    return result;
  }
}
