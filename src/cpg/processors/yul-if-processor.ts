/**
 * Yul If Processor
 * Handles YulIf nodes (if statements in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulIfProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 33; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulIf';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract if statement information
    const ifInfo = this.extractIfInfo(node);

    // Create the Yul if node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: 'yul_if',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulIf: true,
        hasCondition: ifInfo.hasCondition,
        hasBody: ifInfo.hasBody,
        statementCount: ifInfo.statementCount,
        conditionType: ifInfo.conditionType,
        isAlwaysTrue: ifInfo.isAlwaysTrue,
        isAlwaysFalse: ifInfo.isAlwaysFalse,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_if',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractIfInfo(node: SolidityAstNode): {
    hasCondition: boolean;
    hasBody: boolean;
    statementCount: number;
    conditionType: string;
    isAlwaysTrue: boolean;
    isAlwaysFalse: boolean;
  } {
    const nodeAny = node as any;
    
    const condition = nodeAny.condition;
    const body = nodeAny.body;
    
    return {
      hasCondition: !!condition,
      hasBody: !!body,
      statementCount: body?.statements?.length || 0,
      conditionType: condition?.nodeType || 'unknown',
      isAlwaysTrue: this.isAlwaysTrue(condition),
      isAlwaysFalse: this.isAlwaysFalse(condition),
    };
  }

  private isAlwaysTrue(condition: any): boolean {
    if (!condition) return false;
    
    // Check for literal true values
    if (condition.nodeType === 'YulLiteral') {
      const value = condition.value;
      return value === '1' || value === 'true' || (typeof value === 'number' && value !== 0);
    }
    
    return false;
  }

  private isAlwaysFalse(condition: any): boolean {
    if (!condition) return true; // No condition means always false
    
    // Check for literal false values
    if (condition.nodeType === 'YulLiteral') {
      const value = condition.value;
      return value === '0' || value === 'false' || value === '';
    }
    
    return false;
  }
}
