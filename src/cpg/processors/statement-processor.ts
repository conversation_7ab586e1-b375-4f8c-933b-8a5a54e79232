/**
 * Statement Processor
 * Handles statement AST nodes not covered by ExpressionProcessor
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { StatementNode } from '../../types/cpg';

export class StatementProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    const statementTypes = [
      'DoWhileStatement',
      'TryStatement',
      'ThrowStatement',
      'PlaceholderStatement',
      'Continue',
      'Break',
      'RevertStatement',
    ];
    return statementTypes.includes(nodeType);
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 48; // Slightly lower than ExpressionProcessor
  }

  /**
   * Process statement AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Create statement node
    const statementNode: StatementNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: astNode.nodeType.toLowerCase(),
      ...(sourceLocation && { sourceLocation }),
      properties: {
        statementType: astNode.nodeType,
        ...this.extractStatementProperties(astNode),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentStatement is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    // Mark child nodes for processing
    this.markChildNodesForProcessing(astNode, nodeId, childNodeIds);

    const result: ProcessorResult = {
      node: statementNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract statement-specific properties
   */
  private extractStatementProperties(astNode: SolidityAstNode): any {
    const nodeAny = astNode as any;
    const properties: any = {};

    // Common statement properties
    if (nodeAny.condition) {
      properties.hasCondition = true;
    }
    if (nodeAny.body) {
      properties.hasBody = true;
    }
    if (nodeAny.initializationExpression) {
      properties.hasInitialization = true;
    }
    if (nodeAny.loopExpression) {
      properties.hasLoopExpression = true;
    }

    // Try statement specific
    if (nodeAny.clauses) {
      properties.hasCatchClauses = true;
      properties.catchClauseCount = nodeAny.clauses.length;
    }

    return properties;
  }

  /**
   * Mark child nodes for processing
   */
  private markChildNodesForProcessing(
    astNode: SolidityAstNode,
    nodeId: string,
    childNodeIds: string[]
  ): void {
    const nodeAny = astNode as any;

    // Process common child properties
    const childProperties = [
      'condition',
      'body',
      'initializationExpression',
      'loopExpression',
      'clauses',
      'expression',
    ];

    for (const prop of childProperties) {
      const value = nodeAny[prop];
      if (value) {
        if (Array.isArray(value)) {
          value.forEach((_item, index) => {
            childNodeIds.push(`${nodeId}_${prop}_${index}`);
          });
        } else if (typeof value === 'object' && value.nodeType) {
          childNodeIds.push(`${nodeId}_${prop}`);
        }
      }
    }
  }
}
