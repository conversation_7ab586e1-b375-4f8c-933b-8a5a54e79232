/**
 * Conditional Processor
 * Handles Conditional nodes (ternary operators: condition ? trueExpression : falseExpression)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class ConditionalProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 49; // Priority between Expression and Statement
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'Conditional';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Create the conditional expression node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'CONDITIONAL',
      name: 'ternary_operator',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        operator: '?:',
        isExpression: true,
        resultType: (node as any).typeDescriptions?.typeString || 'unknown',
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'conditional_expression',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }
}
