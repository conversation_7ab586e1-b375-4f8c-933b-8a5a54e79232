/**
 * Enum Value Processor
 * Handles EnumValue nodes (individual enum values within enum definitions)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class EnumValueProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 54; // Priority close to EnumProcessor
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'EnumValue';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Create the enum value node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'ENUM_VALUE',
      name: node.name || 'unnamed_enum_value',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        canonicalName: (node as any).canonicalName || node.name,
        isEnumValue: true,
        enumIndex: this.calculateEnumIndex(node, parentId),
      },
    };

    // Add CONTAINS edge from parent enum
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'enum_value',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // No child nodes for enum values
    };
  }

  private calculateEnumIndex(
    node: SolidityAstNode,
    _parentId?: string
  ): number {
    // Try to get the index from the node itself
    const nodeAny = node as any;
    if (typeof nodeAny.index === 'number') {
      return nodeAny.index;
    }

    // If not available, we'll return 0 as a fallback
    // In a real implementation, we might want to track the order
    // of enum values as they're processed
    return 0;
  }
}
