/**
 * Yul Expression Statement Processor
 * Handles YulExpressionStatement nodes (expression statements in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulExpressionStatementProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 37; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulExpressionStatement';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract expression statement information
    const stmtInfo = this.extractStatementInfo(node);

    // Create the Yul expression statement node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: 'yul_expression_statement',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulExpressionStatement: true,
        hasExpression: stmtInfo.hasExpression,
        expressionType: stmtInfo.expressionType,
        isVoidExpression: stmtInfo.isVoidExpression,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_expression_statement',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractStatementInfo(node: SolidityAstNode): {
    hasExpression: boolean;
    expressionType: string;
    isVoidExpression: boolean;
  } {
    const nodeAny = node as any;
    const expression = nodeAny.expression;
    
    return {
      hasExpression: !!expression,
      expressionType: expression?.nodeType || 'unknown',
      isVoidExpression: this.isVoidExpression(expression),
    };
  }

  private isVoidExpression(expression: any): boolean {
    if (!expression) return true;
    
    // Check if this is a function call that doesn't return a value
    if (expression.nodeType === 'YulFunctionCall') {
      const functionName = expression.functionName?.name;
      const voidFunctions = [
        'stop', 'return', 'revert', 'invalid',
        'mstore', 'mstore8', 'sstore',
        'log0', 'log1', 'log2', 'log3', 'log4',
        'selfdestruct'
      ];
      return voidFunctions.includes(functionName);
    }
    
    return false;
  }
}
