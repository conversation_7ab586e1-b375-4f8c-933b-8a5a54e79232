/**
 * Function Call Options Processor
 * Handles FunctionCallOptions nodes (function calls with options like {value: ..., gas: ...})
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class FunctionCallOptionsProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 51; // Priority between Expression and Statement
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'FunctionCallOptions';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract options information
    const options = this.extractOptions(node);

    // Create the function call options node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'FUNCTION_CALL_OPTIONS',
      name: 'function_call_with_options',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        hasValue: options.hasValue,
        hasGas: options.hasGas,
        hasSalt: options.hasSalt,
        optionCount: options.count,
        isPayable: options.hasValue,
        isLowLevel: this.isLowLevelCall(node),
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'function_call_options',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractOptions(node: SolidityAstNode): {
    hasValue: boolean;
    hasGas: boolean;
    hasSalt: boolean;
    count: number;
  } {
    const nodeAny = node as any;
    const names = nodeAny.names || [];

    return {
      hasValue: names.includes('value'),
      hasGas: names.includes('gas'),
      hasSalt: names.includes('salt'),
      count: names.length,
    };
  }

  private isLowLevelCall(node: SolidityAstNode): boolean {
    // Check if this is a low-level call (call, delegatecall, staticcall)
    const nodeAny = node as any;
    const expression = nodeAny.expression;

    if (expression && expression.nodeType === 'MemberAccess') {
      const memberName = expression.memberName;
      const lowLevelCalls = [
        'call',
        'delegatecall',
        'staticcall',
        'send',
        'transfer',
      ];
      return lowLevelCalls.includes(memberName);
    }

    return false;
  }
}
