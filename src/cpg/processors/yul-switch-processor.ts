/**
 * Yul Switch Processor
 * Handles YulSwitch nodes (switch statements in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulSwitchProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 35; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulSwitch';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract switch information
    const switchInfo = this.extractSwitchInfo(node);

    // Create the Yul switch node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'STATEMENT',
      name: 'yul_switch',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulSwitch: true,
        hasExpression: switchInfo.hasExpression,
        caseCount: switchInfo.caseCount,
        hasDefaultCase: switchInfo.hasDefaultCase,
        complexity: switchInfo.complexity,
        isExhaustive: switchInfo.isExhaustive,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_switch',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractSwitchInfo(node: SolidityAstNode): {
    hasExpression: boolean;
    caseCount: number;
    hasDefaultCase: boolean;
    complexity: 'LOW' | 'MEDIUM' | 'HIGH';
    isExhaustive: boolean;
  } {
    const nodeAny = node as any;
    const cases = nodeAny.cases || [];
    
    // Check for default case
    const hasDefaultCase = cases.some((c: any) => c.value === 'default' || !c.value);
    
    return {
      hasExpression: !!nodeAny.expression,
      caseCount: cases.length,
      hasDefaultCase,
      complexity: this.calculateComplexity(cases),
      isExhaustive: hasDefaultCase, // Simple heuristic
    };
  }

  private calculateComplexity(cases: any[]): 'LOW' | 'MEDIUM' | 'HIGH' {
    const caseCount = cases.length;
    
    // Calculate total statements across all cases
    let totalStatements = 0;
    for (const caseNode of cases) {
      if (caseNode.body && caseNode.body.statements) {
        totalStatements += caseNode.body.statements.length;
      }
    }
    
    if (caseCount <= 2 && totalStatements <= 5) return 'LOW';
    if (caseCount <= 5 && totalStatements <= 15) return 'MEDIUM';
    return 'HIGH';
  }
}
