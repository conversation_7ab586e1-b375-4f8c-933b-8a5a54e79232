/**
 * Yul Assignment Processor
 * Handles YulAssignment nodes (assignment statements in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulAssignmentProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 43; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulAssignment';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract assignment information
    const assignmentInfo = this.extractAssignmentInfo(node);

    // Create the Yul assignment node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'ASSIGNMENT',
      name: 'yul_assignment',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulAssignment: true,
        operator: '=',
        variableCount: assignmentInfo.variableCount,
        hasMultipleTargets: assignmentInfo.hasMultipleTargets,
        isMemoryOperation: assignmentInfo.isMemoryOperation,
        isStorageOperation: assignmentInfo.isStorageOperation,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_assignment',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractAssignmentInfo(node: SolidityAstNode): {
    variableCount: number;
    hasMultipleTargets: boolean;
    isMemoryOperation: boolean;
    isStorageOperation: boolean;
  } {
    const nodeAny = node as any;
    const variableNames = nodeAny.variableNames || [];
    
    return {
      variableCount: variableNames.length,
      hasMultipleTargets: variableNames.length > 1,
      isMemoryOperation: this.isMemoryOperation(nodeAny.value),
      isStorageOperation: this.isStorageOperation(nodeAny.value),
    };
  }

  private isMemoryOperation(value: any): boolean {
    if (!value) return false;
    
    // Check if the value involves memory operations
    const memoryOps = ['mload', 'mstore', 'mstore8'];
    return this.containsOperations(value, memoryOps);
  }

  private isStorageOperation(value: any): boolean {
    if (!value) return false;
    
    // Check if the value involves storage operations
    const storageOps = ['sload', 'sstore'];
    return this.containsOperations(value, storageOps);
  }

  private containsOperations(node: any, operations: string[]): boolean {
    if (!node) return false;
    
    // Check if this node is one of the operations
    if (node.name && operations.includes(node.name)) {
      return true;
    }

    // Check function calls
    if (node.nodeType === 'YulFunctionCall' && operations.includes(node.functionName?.name)) {
      return true;
    }

    // Recursively check nested nodes
    const childProps = ['arguments', 'value', 'expression'];
    for (const prop of childProps) {
      if (node[prop]) {
        if (Array.isArray(node[prop])) {
          if (node[prop].some((child: any) => this.containsOperations(child, operations))) {
            return true;
          }
        } else if (this.containsOperations(node[prop], operations)) {
          return true;
        }
      }
    }

    return false;
  }
}
