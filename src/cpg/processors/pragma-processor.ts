/**
 * Pragma Processor
 * Handles PragmaDirective AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { PragmaNode } from '../../types/cpg';

export class PragmaProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'PragmaDirective';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 90; // High priority for pragma directives
  }

  /**
   * Process PragmaDirective AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract pragma information
    const pragmaInfo = this.extractPragmaInfo(astNode);

    // Create pragma node
    const pragmaNode: PragmaNode = {
      id: nodeId,
      type: 'PRAGMA',
      name: `pragma_${pragmaInfo.directive}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        pragmaType: pragmaInfo.directive,
        ...(pragmaInfo.version && { version: pragmaInfo.version }),
        features: pragmaInfo.features,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (SourceUnit)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: pragmaNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract pragma information from AST node
   */
  private extractPragmaInfo(astNode: SolidityAstNode): {
    directive: string;
    value: string;
    version: string | undefined;
    features: string[];
  } {
    const nodeAny = astNode as any;
    const literals = nodeAny.literals || [];

    // First literal is usually the directive (e.g., 'solidity')
    const directive = literals[0] || 'unknown';

    // Remaining literals are the value/version
    const value = literals.slice(1).join(' ') || '';

    // Extract version if it's a solidity pragma
    let version: string | undefined;
    if (directive === 'solidity' && value) {
      version = value;
    }

    // Extract features for other pragma types
    const features = directive !== 'solidity' ? literals.slice(1) : [];

    return {
      directive,
      value,
      version,
      features,
    };
  }
}
