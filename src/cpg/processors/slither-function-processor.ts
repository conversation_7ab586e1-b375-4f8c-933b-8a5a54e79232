/**
 * Slither Function Processor
 * Handles function elements from Slither detector output
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { FunctionNode } from '../../types/cpg';

export class SlitherFunctionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given element type
   */
  canProcess(elementType: string): boolean {
    return elementType === 'function';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 80; // High priority for functions
  }

  /**
   * Process AST node (required by BaseProcessor)
   */
  process(
    _astNode: any,
    _filePath: string,
    _parentId?: string
  ): ProcessorResult {
    // This is not used for Slither processors, but required by interface
    throw new Error('Use processSlitherElement instead');
  }

  /**
   * Process Slither function element
   */
  processSlitherElement(
    element: any,
    filePath: string,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.extractSlitherSourceLocation(
      element.source_mapping,
      filePath
    );

    const parent = element.type_specific_fields?.parent;
    const functionName = parent?.name || element.name || 'unknown';
    const signature =
      parent?.signature || element.signature || `${functionName}()`;

    const functionNode: FunctionNode = {
      id: nodeId,
      type: 'FUNCTION',
      name: functionName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        signature,
        visibility: 'public', // Default, could be extracted from more detailed AST
        stateMutability: 'nonpayable', // Default
        modifiers: [],
        parameters: [],
        returns: [],
      },
    };

    // Update function map for reference by other elements
    functionMap.set(signature, nodeId);

    // Update analysis context
    this.context.updateAnalysisContext({
      currentFunction: signature,
    });

    // Create edges
    const edges = [];

    // Create CONTAINS edge from contract to function
    if (parent?.type === 'contract') {
      const contractName = parent.name;
      const contractId = contractMap.get(contractName);
      if (contractId) {
        edges.push(this.createContainsEdge(contractId, nodeId));
      }
    }

    const result: ProcessorResult = {
      node: functionNode,
      edges,
      childNodeIds: [],
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract source location from Slither source mapping
   */
  private extractSlitherSourceLocation(
    sourceMapping: any,
    filePath: string
  ): any {
    if (!sourceMapping) return undefined;

    return {
      start: sourceMapping.start || 0,
      length: sourceMapping.length || 0,
      lines: sourceMapping.lines || [],
      startColumn: sourceMapping.starting_column || 0,
      endColumn: sourceMapping.ending_column || 0,
      filename: sourceMapping.filename_short || filePath,
    };
  }
}
