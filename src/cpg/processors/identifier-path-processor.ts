/**
 * Identifier Path Processor
 * Handles IdentifierPath AST nodes (qualified identifiers like MyContract.MyFunction)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class IdentifierPathProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'IdentifierPath';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 42; // Medium priority - between expressions and utility nodes
  }

  /**
   * Process IdentifierPath AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract identifier path information
    const pathInfo = this.extractPathInfo(astNode);

    // Create identifier path node as an expression
    const identifierPathNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: pathInfo.fullPath,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'IdentifierPath',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          fullPath: pathInfo.fullPath,
          segments: pathInfo.segments,
          isQualifiedIdentifier: true,
          referencedDeclaration: pathInfo.referencedDeclaration,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: identifierPathNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract identifier path information from AST node
   */
  private extractPathInfo(astNode: SolidityAstNode): {
    fullPath: string;
    segments: string[];
    referencedDeclaration?: number;
  } {
    const nodeAny = astNode as any;
    
    // Extract the full path name
    const fullPath = nodeAny.name || 'unknown';
    
    // Split into segments (e.g., "MyContract.MyFunction" -> ["MyContract", "MyFunction"])
    const segments = fullPath.split('.');
    
    // Extract referenced declaration ID if available
    const referencedDeclaration = nodeAny.referencedDeclaration;

    return {
      fullPath,
      segments,
      referencedDeclaration,
    };
  }
}
