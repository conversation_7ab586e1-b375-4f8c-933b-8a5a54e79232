/**
 * Override Specifier Processor
 * Handles OverrideSpecifier AST nodes (virtual/override keywords)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class OverrideSpecifierProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'OverrideSpecifier';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 41; // Medium priority - modifier-related node
  }

  /**
   * Process OverrideSpecifier AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract override information
    const overrideInfo = this.extractOverrideInfo(astNode);

    // Create override specifier node as an expression
    const overrideSpecifierNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: overrideInfo.specifierName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'OverrideSpecifier',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          specifierType: overrideInfo.specifierType,
          overriddenContracts: overrideInfo.overriddenContracts,
          isOverrideSpecifier: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (function)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child override nodes for processing
    if (astNode['overrides']) {
      astNode['overrides']['forEach']((_override: any, index: number) => {
        childNodeIds.push(`${nodeId}_override_${index}`);
      });
    }

    // Update analysis context for override tracking
    this.updateOverrideAnalysis(nodeId, overrideInfo, parentId);

    const result: ProcessorResult = {
      node: overrideSpecifierNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract override specifier information from AST node
   */
  private extractOverrideInfo(astNode: SolidityAstNode): {
    specifierName: string;
    specifierType: string;
    overriddenContracts: string[];
  } {
    const nodeAny = astNode as any;
    
    // Determine specifier type (override or virtual)
    const specifierType = 'override'; // OverrideSpecifier is always for override
    
    // Extract overridden contracts if specified
    const overriddenContracts: string[] = [];
    if (nodeAny.overrides && Array.isArray(nodeAny.overrides)) {
      nodeAny.overrides.forEach((override: any) => {
        if (override.name) {
          overriddenContracts.push(override.name);
        } else if (override.pathNode?.name) {
          overriddenContracts.push(override.pathNode.name);
        }
      });
    }
    
    // Generate descriptive name
    let specifierName = 'override';
    if (overriddenContracts.length > 0) {
      specifierName = `override(${overriddenContracts.join(', ')})`;
    }

    return {
      specifierName,
      specifierType,
      overriddenContracts,
    };
  }

  /**
   * Update analysis context for override tracking
   */
  private updateOverrideAnalysis(
    nodeId: string, 
    overrideInfo: any, 
    parentId?: string
  ): void {
    const context = this.context.getAnalysisContext();
    
    // Track override specifiers for inheritance analysis
    if (!(context as any).overrideSpecifiers) {
      (context as any).overrideSpecifiers = new Map();
    }
    
    // Associate override specifier with its parent function
    if (parentId) {
      (context as any).overrideSpecifiers.set(parentId, {
        nodeId,
        specifierType: overrideInfo.specifierType,
        overriddenContracts: overrideInfo.overriddenContracts,
      });
    }
  }
}
