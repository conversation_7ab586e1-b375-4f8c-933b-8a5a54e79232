/**
 * Slither Variable Processor
 * Handles variable elements from Slither detector output
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { VariableNode, VariableScope } from '../../types/cpg';

export class SlitherVariableProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given element type
   */
  canProcess(elementType: string): boolean {
    return elementType === 'variable';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 70; // Medium-high priority for variables
  }

  /**
   * Process AST node (required by BaseProcessor)
   */
  process(
    _astNode: any,
    _filePath: string,
    _parentId?: string
  ): ProcessorResult {
    // This is not used for Slither processors, but required by interface
    throw new Error('Use processSlitherElement instead');
  }

  /**
   * Process Slither variable element
   */
  processSlitherElement(
    element: any,
    filePath: string,
    contractMap: Map<string, string>,
    functionMap: Map<string, string>
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.extractSlitherSourceLocation(
      element.source_mapping,
      filePath
    );

    const parent = element.type_specific_fields?.parent;
    const variableName = element.name;
    const isParameter = element.additional_fields?.target === 'parameter';

    // Determine variable scope and if it's a state variable
    let scope: VariableScope = 'LOCAL';
    let isStateVariable = false;

    if (isParameter) {
      scope = 'PARAMETER';
    } else if (parent?.type === 'contract') {
      scope = 'STATE';
      isStateVariable = true;
      // Update context to track state variables
      const context = this.context.getAnalysisContext();
      context.stateVariables.add(nodeId);
    } else if (parent?.type === 'function') {
      scope = 'LOCAL';
    }

    const variableNode: VariableNode = {
      id: nodeId,
      type: 'VARIABLE',
      name: variableName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        variableType: 'uint256', // Default, could be extracted from more detailed AST
        scope,
        isStateVariable,
        isTainted: false, // Will be determined during taint analysis
      },
    };

    // Update analysis context
    const context = this.context.getAnalysisContext();
    context.variableScopes.set(nodeId, scope);

    // Mark parameters as potentially tainted (user input)
    if (isParameter) {
      context.taintedVariables.add(nodeId);
      variableNode.properties.isTainted = true;
      variableNode.properties.taintSource = 'user_input';
    }

    // Create edges
    const edges = [];

    // Create CONTAINS edge from parent to variable
    if (parent) {
      let parentId: string | undefined;

      if (parent.type === 'function') {
        const signature = parent.signature || `${parent.name}()`;
        parentId = functionMap.get(signature);
      } else if (parent.type === 'contract') {
        parentId = contractMap.get(parent.name);
      }

      if (parentId) {
        edges.push(this.createContainsEdge(parentId, nodeId));
      }
    }

    const result: ProcessorResult = {
      node: variableNode,
      edges,
      childNodeIds: [],
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract source location from Slither source mapping
   */
  private extractSlitherSourceLocation(
    sourceMapping: any,
    filePath: string
  ): any {
    if (!sourceMapping) return undefined;

    return {
      start: sourceMapping.start || 0,
      length: sourceMapping.length || 0,
      lines: sourceMapping.lines || [],
      startColumn: sourceMapping.starting_column || 0,
      endColumn: sourceMapping.ending_column || 0,
      filename: sourceMapping.filename_short || filePath,
    };
  }
}
