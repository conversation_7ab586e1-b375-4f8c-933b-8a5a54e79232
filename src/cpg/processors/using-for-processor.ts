/**
 * Using For Processor
 * Handles UsingForDirective AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { UsingForNode } from '../../types/cpg';

export class UsingForProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'UsingForDirective';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 68; // Medium-high priority for using directives
  }

  /**
   * Process UsingForDirective AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    const nodeAny = astNode as any;

    // Extract using for information
    const libraryName = this.extractLibraryName(nodeAny);
    const typeName = this.extractTypeName(nodeAny);
    const functions = this.extractFunctions(nodeAny);

    // Create using for node
    const usingForNode: UsingForNode = {
      id: nodeId,
      type: 'USING_FOR',
      name: `using_${libraryName}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        libraryName,
        typeName,
        functions,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentUsingFor is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    const result: ProcessorResult = {
      node: usingForNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract library name from AST node
   */
  private extractLibraryName(nodeAny: any): string {
    return (
      nodeAny.libraryName?.name ||
      nodeAny.functions?.[0]?.function?.name ||
      'unknown'
    );
  }

  /**
   * Extract type name from AST node
   */
  private extractTypeName(nodeAny: any): string {
    return (
      nodeAny.typeName?.typeDescriptions?.typeString ||
      nodeAny.typeName?.name ||
      'unknown'
    );
  }

  /**
   * Extract functions from AST node
   */
  private extractFunctions(nodeAny: any): string[] {
    return (
      nodeAny.functions?.map((func: any) => func.function?.name || 'unknown') ||
      []
    );
  }
}
