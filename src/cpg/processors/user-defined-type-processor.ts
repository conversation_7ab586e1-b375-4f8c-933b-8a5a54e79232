/**
 * User Defined Type Processor
 * Handles UserDefinedTypeName AST nodes (custom types like struct names, contract names)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class UserDefinedTypeProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'UserDefinedTypeName';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 38; // Medium priority - type information node
  }

  /**
   * Process UserDefinedTypeName AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract user-defined type information
    const typeInfo = this.extractUserDefinedTypeInfo(astNode);

    // Create user-defined type node as an expression
    const userDefinedTypeNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: typeInfo.typeName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'UserDefinedTypeName',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          typeName: typeInfo.typeName,
          typeCategory: typeInfo.typeCategory,
          referencedDeclaration: typeInfo.referencedDeclaration,
          isUserDefinedType: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child path nodes for processing if present
    if (astNode['pathNode']) {
      childNodeIds.push(`${nodeId}_path`);
    }

    const result: ProcessorResult = {
      node: userDefinedTypeNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract user-defined type information from AST node
   */
  private extractUserDefinedTypeInfo(astNode: SolidityAstNode): {
    typeName: string;
    typeCategory: string;
    referencedDeclaration?: number;
  } {
    const nodeAny = astNode as any;

    // Extract type name from various possible locations
    const typeName =
      nodeAny.name ||
      nodeAny.pathNode?.name ||
      nodeAny.typeDescriptions?.typeString ||
      'unknown';

    // Determine type category based on referenced declaration or name patterns
    let typeCategory = 'unknown';
    if (nodeAny.typeDescriptions?.typeString) {
      const typeString = nodeAny.typeDescriptions.typeString;
      if (typeString.includes('struct')) {
        typeCategory = 'struct';
      } else if (typeString.includes('contract')) {
        typeCategory = 'contract';
      } else if (typeString.includes('enum')) {
        typeCategory = 'enum';
      } else if (typeString.includes('library')) {
        typeCategory = 'library';
      } else {
        typeCategory = 'custom';
      }
    }

    // Extract referenced declaration ID if available
    const referencedDeclaration = nodeAny.referencedDeclaration;

    return {
      typeName,
      typeCategory,
      referencedDeclaration,
    };
  }
}
