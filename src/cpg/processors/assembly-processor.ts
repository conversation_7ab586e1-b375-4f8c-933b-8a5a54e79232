/**
 * Assembly Block Processor
 * Handles InlineAssembly AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { AssemblyBlockNode } from '../../types/cpg';

export class AssemblyProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'InlineAssembly';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 45; // Lower priority for assembly blocks
  }

  /**
   * Process InlineAssembly AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract assembly operations
    const operations = this.extractAssemblyOperations((astNode as any).body);
    const dialect = (astNode as any).dialect || 'yul';

    // Create assembly block node
    const assemblyNode: AssemblyBlockNode = {
      id: nodeId,
      type: 'ASSEMBLY_BLOCK',
      name: 'assembly_block',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        dialect,
        operations,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (function)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentAssemblyBlock is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    const result: ProcessorResult = {
      node: assemblyNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract assembly operations from body node
   */
  private extractAssemblyOperations(bodyNode?: any): string[] {
    if (!bodyNode || !bodyNode.statements) {
      return [];
    }

    const operations: string[] = [];

    const extractFromStatement = (stmt: any): void => {
      if (stmt.nodeType) {
        operations.push(stmt.nodeType);
      }

      // Recursively extract from nested statements
      if (stmt.statements) {
        stmt.statements.forEach(extractFromStatement);
      }
      if (stmt.body) {
        extractFromStatement(stmt.body);
      }
    };

    bodyNode.statements.forEach(extractFromStatement);
    return operations;
  }
}
