/**
 * Modifier Invocation Processor
 * Handles ModifierInvocation AST nodes (modifier calls like only<PERSON><PERSON><PERSON>, nonReentrant)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode, CallsEdge } from '../../types/cpg';

export class ModifierInvocationProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ModifierInvocation';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 72; // High priority - between modifiers and variables
  }

  /**
   * Process ModifierInvocation AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract modifier information
    const modifierInfo = this.extractModifierInfo(astNode);

    // Create modifier invocation node as an expression
    const modifierInvocationNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: modifierInfo.name,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'ModifierInvocation',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          modifierName: modifierInfo.name,
          arguments: modifierInfo.arguments,
          isModifierCall: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (function)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Create CALLS edge to the modifier definition if it exists
    const modifierTargetId = `modifier_${modifierInfo.name}`;
    const targetModifier = this.context.getNode(modifierTargetId);
    if (targetModifier) {
      const callsEdge: CallsEdge = {
        id: this.context.generateEdgeId(),
        type: 'CALLS',
        source: nodeId,
        target: modifierTargetId,
        properties: {
          callType: 'modifier',
        },
      };
      edges.push(callsEdge);
    } else {
      console.log(
        `⚠️ Skipping CALLS edge to non-existent modifier: ${modifierInfo.name}`
      );
    }

    // Mark child argument nodes for processing
    if (astNode['arguments']) {
      astNode['arguments']['forEach']((_arg: any, index: number) => {
        childNodeIds.push(`${nodeId}_arg_${index}`);
      });
    }

    // Update analysis context to track modifier usage
    const context = this.context.getAnalysisContext();
    if (!(context as any).modifierInvocations) {
      (context as any).modifierInvocations = new Map();
    }

    // Track which function uses which modifiers
    if (parentId) {
      const existingModifiers =
        (context as any).modifierInvocations.get(parentId) || [];
      existingModifiers.push(modifierInfo.name);
      (context as any).modifierInvocations.set(parentId, existingModifiers);
    }

    const result: ProcessorResult = {
      node: modifierInvocationNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract modifier information from AST node
   */
  private extractModifierInfo(astNode: SolidityAstNode): {
    name: string;
    arguments: string[];
  } {
    const nodeAny = astNode as any;

    // Extract modifier name from modifierName or kind
    const name =
      nodeAny.modifierName?.name || nodeAny.kind || nodeAny.name || 'unknown';

    // Extract arguments if present
    const args: string[] = [];
    if (nodeAny.arguments && Array.isArray(nodeAny.arguments)) {
      nodeAny.arguments.forEach((arg: any) => {
        if (arg.name) {
          args.push(arg.name);
        } else if (arg.value) {
          args.push(arg.value.toString());
        } else {
          args.push('unknown');
        }
      });
    }

    return { name, arguments: args };
  }
}
