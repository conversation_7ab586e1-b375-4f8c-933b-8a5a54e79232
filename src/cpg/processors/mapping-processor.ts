/**
 * Mapping Processor
 * Handles Mapping AST nodes (mapping type definitions like mapping(address => uint256))
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class MappingProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'Mapping';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 37; // Medium priority - type information node
  }

  /**
   * Process Mapping AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract mapping information
    const mappingInfo = this.extractMappingInfo(astNode);

    // Create mapping node as an expression
    const mappingNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: mappingInfo.fullTypeName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'Mapping',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          fullTypeName: mappingInfo.fullTypeName,
          keyType: mappingInfo.keyType,
          valueType: mappingInfo.valueType,
          isNestedMapping: mappingInfo.isNestedMapping,
          nestingLevel: mappingInfo.nestingLevel,
          isMappingType: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing
    if (astNode['keyType']) {
      childNodeIds.push(`${nodeId}_keyType`);
    }
    if (astNode['valueType']) {
      childNodeIds.push(`${nodeId}_valueType`);
    }

    const result: ProcessorResult = {
      node: mappingNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract mapping information from AST node
   */
  private extractMappingInfo(astNode: SolidityAstNode): {
    fullTypeName: string;
    keyType: string;
    valueType: string;
    isNestedMapping: boolean;
    nestingLevel: number;
  } {
    const nodeAny = astNode as any;
    
    // Extract key type
    const keyType = this.extractTypeName(nodeAny.keyType);
    
    // Extract value type
    const valueType = this.extractTypeName(nodeAny.valueType);
    
    // Check if this is a nested mapping (value type is also a mapping)
    const isNestedMapping = nodeAny.valueType?.nodeType === 'Mapping';
    
    // Calculate nesting level for nested mappings
    const nestingLevel = this.calculateNestingLevel(nodeAny, 1);
    
    // Build full type name
    const fullTypeName = `mapping(${keyType} => ${valueType})`;

    return {
      fullTypeName,
      keyType,
      valueType,
      isNestedMapping,
      nestingLevel,
    };
  }

  /**
   * Extract type name from a type node
   */
  private extractTypeName(typeNode: any): string {
    if (!typeNode) return 'unknown';
    
    if (typeNode.nodeType === 'ElementaryTypeName') {
      return typeNode.name || 'unknown';
    } else if (typeNode.nodeType === 'UserDefinedTypeName') {
      return typeNode.name || typeNode.pathNode?.name || 'unknown';
    } else if (typeNode.nodeType === 'Mapping') {
      // Recursive case for nested mappings
      const keyType = this.extractTypeName(typeNode.keyType);
      const valueType = this.extractTypeName(typeNode.valueType);
      return `mapping(${keyType} => ${valueType})`;
    } else if (typeNode.nodeType === 'ArrayTypeName') {
      const baseType = this.extractTypeName(typeNode.baseType);
      return typeNode.length ? `${baseType}[${typeNode.length}]` : `${baseType}[]`;
    } else if (typeNode.typeDescriptions?.typeString) {
      return typeNode.typeDescriptions.typeString;
    }
    
    return typeNode.name || 'unknown';
  }

  /**
   * Calculate nesting level for nested mappings
   */
  private calculateNestingLevel(mappingNode: any, currentLevel: number): number {
    if (!mappingNode.valueType || mappingNode.valueType.nodeType !== 'Mapping') {
      return currentLevel;
    }
    
    return this.calculateNestingLevel(mappingNode.valueType, currentLevel + 1);
  }
}
