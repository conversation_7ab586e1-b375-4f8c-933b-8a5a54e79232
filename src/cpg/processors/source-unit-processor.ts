/**
 * Source Unit Processor
 * Handles SourceUnit AST nodes (root of the AST)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class SourceUnitProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'SourceUnit';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 110; // Highest priority - root node
  }

  /**
   * Process SourceUnit AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract source unit information
    const sourceInfo = this.extractSourceInfo(astNode, filePath);

    // Create source unit node as an expression
    const sourceUnitNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: 'source_unit',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'SourceUnit',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          filePath,
          childCount: sourceInfo.childCount,
          hasContracts: sourceInfo.hasContracts,
          hasPragmas: sourceInfo.hasPragmas,
          hasImports: sourceInfo.hasImports,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (if any)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing
    if (astNode['nodes']) {
      astNode['nodes']['forEach']((_child: any, index: number) => {
        childNodeIds.push(`${nodeId}_child_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: sourceUnitNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract source unit information from AST node
   */
  private extractSourceInfo(
    astNode: SolidityAstNode,
    _filePath: string
  ): {
    childCount: number;
    hasContracts: boolean;
    hasPragmas: boolean;
    hasImports: boolean;
  } {
    const nodes = astNode['nodes'] || [];
    const childCount = Array.isArray(nodes) ? nodes.length : 0;

    let hasContracts = false;
    let hasPragmas = false;
    let hasImports = false;

    if (Array.isArray(nodes)) {
      for (const node of nodes) {
        if (node.nodeType === 'ContractDefinition') {
          hasContracts = true;
        } else if (node.nodeType === 'PragmaDirective') {
          hasPragmas = true;
        } else if (node.nodeType === 'ImportDirective') {
          hasImports = true;
        }
      }
    }

    return {
      childCount,
      hasContracts,
      hasPragmas,
      hasImports,
    };
  }
}
