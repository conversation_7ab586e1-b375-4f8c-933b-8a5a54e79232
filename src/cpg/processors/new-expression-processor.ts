/**
 * New Expression Processor
 * Handles NewExpression AST nodes (constructor calls, array creation)
 */

import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';
import { BaseProcessor, ProcessorResult } from './base-processor';

export class NewExpressionProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'NewExpression';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 45; // Medium priority - expression-level
  }

  /**
   * Process NewExpression AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract new expression information
    const typeInfo = this.extractNewExpressionTypeInfo(astNode);
    const constructorInfo = this.extractConstructorInfo(astNode);

    // Create new expression node
    const newExpressionNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: `new_${typeInfo.typeName}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'NewExpression',
        value: `new ${typeInfo.typeName}(${constructorInfo.argumentCount} args)`,
        isTainted: constructorInfo.hasTaintedArguments,
        taintSources: constructorInfo.taintSources,
        // Additional properties stored as custom fields
        ...({
          typeName: typeInfo.typeName,
          typeString: typeInfo.typeString,
          isArrayCreation: typeInfo.isArray,
          isContractCreation: typeInfo.isContract,
          constructorArguments: constructorInfo.arguments,
          argumentCount: constructorInfo.argumentCount,
        } as any),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: newExpressionNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract type information from NewExpression
   */
  protected extractNewExpressionTypeInfo(astNode: SolidityAstNode): {
    typeName: string;
    typeString: string;
    isArray: boolean;
    isContract: boolean;
  } {
    const astNodeAny = astNode as any;
    const typeName =
      astNodeAny['typeName']?.name ||
      astNodeAny['typeName']?.nodeType ||
      'unknown';

    const typeString =
      astNode.typeDescriptions?.typeString ||
      astNodeAny['typeName']?.typeDescriptions?.typeString ||
      'unknown';

    // Determine if this is array creation
    const isArray =
      typeString.includes('[]') ||
      astNodeAny['typeName']?.nodeType === 'ArrayTypeName' ||
      typeName.includes('Array');

    // Determine if this is contract creation
    const isContract =
      astNodeAny['typeName']?.nodeType === 'UserDefinedTypeName' && !isArray;

    return {
      typeName,
      typeString,
      isArray,
      isContract,
    };
  }

  /**
   * Extract constructor argument information
   */
  protected extractConstructorInfo(astNode: SolidityAstNode): {
    arguments: string[];
    argumentCount: number;
    hasTaintedArguments: boolean;
    taintSources: string[];
  } {
    const constructorArgs: string[] = [];
    let hasTaintedArguments = false;
    const taintSources: string[] = [];

    // Extract arguments if present
    const astNodeAny = astNode as any;
    if (astNodeAny['arguments'] && Array.isArray(astNodeAny['arguments'])) {
      for (const arg of astNodeAny['arguments']) {
        const argName = arg.name || arg.nodeType || 'unknown';
        constructorArgs.push(argName);

        // Check if argument is potentially tainted
        if (this.isArgumentTainted(arg)) {
          hasTaintedArguments = true;
          taintSources.push(argName);
        }
      }
    }

    return {
      arguments: constructorArgs,
      argumentCount: constructorArgs.length,
      hasTaintedArguments,
      taintSources,
    };
  }

  /**
   * Check if an argument is potentially tainted
   */
  protected isArgumentTainted(arg: any): boolean {
    // Arguments from external sources are considered tainted
    if (arg.nodeType === 'Identifier') {
      // Check if it's a parameter or external variable
      return (
        arg.name?.startsWith('_') || arg.referencedDeclaration !== undefined // Convention for parameters
      ); // References external declaration
    }

    if (arg.nodeType === 'MemberAccess') {
      // msg.sender, msg.value, etc. are tainted
      return (
        arg.expression?.name === 'msg' ||
        arg.expression?.name === 'tx' ||
        arg.expression?.name === 'block'
      );
    }

    if (arg.nodeType === 'FunctionCall') {
      // External function calls are tainted
      return true;
    }

    return false;
  }
}
