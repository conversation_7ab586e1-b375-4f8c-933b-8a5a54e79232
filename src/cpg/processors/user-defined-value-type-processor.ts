/**
 * User Defined Value Type Processor
 * Handles UserDefinedValueTypeDefinition nodes (type aliases)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class UserDefinedValueTypeProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 39; // Priority between UserDefinedType and ElementaryType
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'UserDefinedValueTypeDefinition';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Create the type alias node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'TYPE_ALIAS',
      name: node.name || 'unnamed_type_alias',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        canonicalName: node.canonicalName || node.name,
        underlyingType:
          (node as any).underlyingType?.typeDescriptions?.typeString ||
          'unknown',
        visibility: 'internal', // Type aliases are always internal
        isUserDefined: true,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'type_definition',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // No child nodes for type aliases
    };
  }
}
