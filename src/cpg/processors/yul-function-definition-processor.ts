/**
 * Yul Function Definition Processor
 * Handles YulFunctionDefinition nodes (function definitions in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulFunctionDefinitionProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 32; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulFunctionDefinition';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract function definition information
    const funcInfo = this.extractFunctionInfo(node);

    // Create the Yul function definition node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'FUNCTION',
      name: funcInfo.name || 'unnamed_yul_function',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulFunction: true,
        functionName: funcInfo.name,
        parameterCount: funcInfo.parameterCount,
        returnValueCount: funcInfo.returnValueCount,
        hasBody: funcInfo.hasBody,
        statementCount: funcInfo.statementCount,
        visibility: 'internal', // Yul functions are always internal
        isAssemblyFunction: true,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_function_definition',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractFunctionInfo(node: SolidityAstNode): {
    name: string;
    parameterCount: number;
    returnValueCount: number;
    hasBody: boolean;
    statementCount: number;
  } {
    const nodeAny = node as any;
    
    const parameters = nodeAny.parameters || [];
    const returnVariables = nodeAny.returnVariables || [];
    const body = nodeAny.body;
    
    return {
      name: nodeAny.name || 'unnamed_yul_function',
      parameterCount: parameters.length,
      returnValueCount: returnVariables.length,
      hasBody: !!body,
      statementCount: body?.statements?.length || 0,
    };
  }
}
