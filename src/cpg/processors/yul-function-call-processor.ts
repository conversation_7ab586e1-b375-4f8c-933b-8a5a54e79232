/**
 * Yul Function Call Processor
 * Handles YulFunctionCall nodes (function calls in Yul assembly)
 */

import {
  BaseProcessor,
  ProcessorContext,
  ProcessorResult,
} from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulFunctionCallProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 41; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulFunctionCall';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract function call information
    const callInfo = this.extractCallInfo(node);

    // Create the Yul function call node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: callInfo.functionName || 'unnamed_yul_call',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulFunctionCall: true,
        functionName: callInfo.functionName,
        argumentCount: callInfo.argumentCount,
        isBuiltinFunction: callInfo.isBuiltinFunction,
        operationType: callInfo.operationType,
        isMemoryOperation: callInfo.isMemoryOperation,
        isStorageOperation: callInfo.isStorageOperation,
        isCryptoOperation: callInfo.isCryptoOperation,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_function_call',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // Child nodes will be processed separately
    };
  }

  private extractCallInfo(node: SolidityAstNode): {
    functionName: string;
    argumentCount: number;
    isBuiltinFunction: boolean;
    operationType: string;
    isMemoryOperation: boolean;
    isStorageOperation: boolean;
    isCryptoOperation: boolean;
  } {
    const nodeAny = node as any;
    const functionName = nodeAny.functionName?.name || 'unknown';
    const args = nodeAny.arguments || [];

    return {
      functionName,
      argumentCount: args.length,
      isBuiltinFunction: this.isBuiltinFunction(functionName),
      operationType: this.getOperationType(functionName),
      isMemoryOperation: this.isMemoryOperation(functionName),
      isStorageOperation: this.isStorageOperation(functionName),
      isCryptoOperation: this.isCryptoOperation(functionName),
    };
  }

  private isBuiltinFunction(functionName: string): boolean {
    const builtinFunctions = [
      // Arithmetic
      'add',
      'sub',
      'mul',
      'div',
      'mod',
      'exp',
      // Comparison
      'lt',
      'gt',
      'eq',
      'iszero',
      // Bitwise
      'and',
      'or',
      'xor',
      'not',
      'shl',
      'shr',
      'sar',
      // Memory
      'mload',
      'mstore',
      'mstore8',
      'msize',
      // Storage
      'sload',
      'sstore',
      // Crypto
      'keccak256',
      'sha256',
      'ripemd160',
      'ecrecover',
      // Block/Transaction
      'blockhash',
      'coinbase',
      'timestamp',
      'number',
      'difficulty',
      'gaslimit',
      'chainid',
      'selfbalance',
      'balance',
      'caller',
      'callvalue',
      'calldataload',
      'calldatasize',
      'calldatacopy',
      'codesize',
      'codecopy',
      'gasprice',
      'extcodesize',
      'extcodecopy',
      'returndatasize',
      'returndatacopy',
      // Control flow
      'stop',
      'return',
      'revert',
      'invalid',
      // Calls
      'call',
      'callcode',
      'delegatecall',
      'staticcall',
      // Create
      'create',
      'create2',
      // Logs
      'log0',
      'log1',
      'log2',
      'log3',
      'log4',
    ];

    return builtinFunctions.includes(functionName);
  }

  private getOperationType(functionName: string): string {
    if (this.isMemoryOperation(functionName)) return 'memory';
    if (this.isStorageOperation(functionName)) return 'storage';
    if (this.isCryptoOperation(functionName)) return 'crypto';
    if (['add', 'sub', 'mul', 'div', 'mod', 'exp'].includes(functionName))
      return 'arithmetic';
    if (['lt', 'gt', 'eq', 'iszero'].includes(functionName))
      return 'comparison';
    if (['and', 'or', 'xor', 'not', 'shl', 'shr', 'sar'].includes(functionName))
      return 'bitwise';
    if (
      ['call', 'callcode', 'delegatecall', 'staticcall'].includes(functionName)
    )
      return 'call';
    if (['create', 'create2'].includes(functionName)) return 'create';
    if (functionName.startsWith('log')) return 'log';
    return 'other';
  }

  private isMemoryOperation(functionName: string): boolean {
    const memoryOps = ['mload', 'mstore', 'mstore8', 'msize'];
    return memoryOps.includes(functionName);
  }

  private isStorageOperation(functionName: string): boolean {
    const storageOps = ['sload', 'sstore'];
    return storageOps.includes(functionName);
  }

  private isCryptoOperation(functionName: string): boolean {
    const cryptoOps = ['keccak256', 'sha256', 'ripemd160', 'ecrecover'];
    return cryptoOps.includes(functionName);
  }
}
