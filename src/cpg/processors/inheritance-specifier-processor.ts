/**
 * Inheritance Specifier Processor
 * Handles InheritanceSpecifier AST nodes (contract inheritance specifications)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class InheritanceSpecifierProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'InheritanceSpecifier';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 43; // Medium priority - inheritance-related node
  }

  /**
   * Process InheritanceSpecifier AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract inheritance information
    const inheritanceInfo = this.extractInheritanceInfo(astNode);

    // Create inheritance specifier node as an expression
    const inheritanceSpecifierNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: inheritanceInfo.inheritanceName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'InheritanceSpecifier',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          baseContractName: inheritanceInfo.baseContractName,
          hasArguments: inheritanceInfo.hasArguments,
          argumentCount: inheritanceInfo.argumentCount,
          isInheritanceSpecifier: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child nodes for processing
    if (astNode['baseName']) {
      childNodeIds.push(`${nodeId}_baseName`);
    }
    if (astNode['arguments']) {
      astNode['arguments']['forEach']((_arg: any, index: number) => {
        childNodeIds.push(`${nodeId}_arg_${index}`);
      });
    }

    // Update analysis context for inheritance tracking
    this.updateInheritanceAnalysis(nodeId, inheritanceInfo, parentId);

    const result: ProcessorResult = {
      node: inheritanceSpecifierNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract inheritance information from AST node
   */
  private extractInheritanceInfo(astNode: SolidityAstNode): {
    inheritanceName: string;
    baseContractName: string;
    hasArguments: boolean;
    argumentCount: number;
  } {
    const nodeAny = astNode as any;
    
    // Extract base contract name
    let baseContractName = 'unknown';
    if (nodeAny.baseName) {
      if (nodeAny.baseName.name) {
        baseContractName = nodeAny.baseName.name;
      } else if (nodeAny.baseName.pathNode?.name) {
        baseContractName = nodeAny.baseName.pathNode.name;
      }
    }
    
    // Check for constructor arguments
    const hasArguments = nodeAny.arguments && Array.isArray(nodeAny.arguments) && nodeAny.arguments.length > 0;
    const argumentCount = hasArguments ? nodeAny.arguments.length : 0;
    
    // Generate descriptive name
    let inheritanceName = `inherits_${baseContractName}`;
    if (hasArguments) {
      inheritanceName += `(${argumentCount}_args)`;
    }

    return {
      inheritanceName,
      baseContractName,
      hasArguments,
      argumentCount,
    };
  }

  /**
   * Update analysis context for inheritance tracking
   */
  private updateInheritanceAnalysis(
    nodeId: string, 
    inheritanceInfo: any, 
    parentId?: string
  ): void {
    const context = this.context.getAnalysisContext();
    
    // Track inheritance relationships for contract analysis
    if (!(context as any).inheritanceRelationships) {
      (context as any).inheritanceRelationships = new Map();
    }
    
    // Associate inheritance with its parent contract
    if (parentId) {
      const existingInheritances = (context as any).inheritanceRelationships.get(parentId) || [];
      existingInheritances.push({
        nodeId,
        baseContractName: inheritanceInfo.baseContractName,
        hasArguments: inheritanceInfo.hasArguments,
        argumentCount: inheritanceInfo.argumentCount,
      });
      (context as any).inheritanceRelationships.set(parentId, existingInheritances);
    }
  }
}
