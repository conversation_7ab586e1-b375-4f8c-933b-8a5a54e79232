/**
 * Parameter List Processor
 * Handles ParameterList AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class ParameterListProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ParameterList';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 40; // Lower priority - utility node
  }

  /**
   * Process ParameterList AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract parameter information
    const parameters = this.extractParameterInfo(astNode);

    // Create parameter list node as an expression
    const parameterListNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: 'parameter_list',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'ParameterList',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          parameterCount: parameters.length,
          parameters,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark child parameter nodes for processing
    if (astNode['parameters']) {
      astNode['parameters']['forEach']((_param: any, index: number) => {
        childNodeIds.push(`${nodeId}_param_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: parameterListNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract parameter information from AST node
   */
  private extractParameterInfo(
    astNode: SolidityAstNode
  ): Array<{ name: string; type: string }> {
    if (!astNode['parameters'] || !Array.isArray(astNode['parameters'])) {
      return [];
    }

    return astNode['parameters'].map((param: any) => ({
      name: param.name || 'unnamed',
      type: param.typeDescriptions?.typeString || 'unknown',
    }));
  }
}
