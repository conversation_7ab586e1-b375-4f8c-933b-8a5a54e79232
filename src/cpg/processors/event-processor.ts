/**
 * Event Processor
 * Handles EventDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { EventNode } from '../../types/cpg';

export class EventProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'EventDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 65; // Medium priority for events
  }

  /**
   * Process EventDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract event information
    const parameters = this.extractParameters(astNode.parameters);
    const isAnonymous = this.isAnonymousEvent(astNode);

    // Create event node
    const eventNode: EventNode = {
      id: nodeId,
      type: 'EVENT',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters,
        anonymous: isAnonymous,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Mark parameters for processing
    if (astNode.parameters?.parameters) {
      astNode.parameters.parameters['forEach']((_param: any, index: number) => {
        childNodeIds.push(`${nodeId}_param_${index}`);
      });
    }

    const result: ProcessorResult = {
      node: eventNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Check if event is anonymous
   */
  private isAnonymousEvent(astNode: SolidityAstNode): boolean {
    return Boolean(astNode['anonymous'] === true);
  }
}
