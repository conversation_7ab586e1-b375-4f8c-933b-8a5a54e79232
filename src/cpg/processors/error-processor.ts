/**
 * Error Processor
 * Handles ErrorDefinition AST nodes
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ErrorNode } from '../../types/cpg';

export class ErrorProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ErrorDefinition';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 58; // Medium priority for errors
  }

  /**
   * Process ErrorDefinition AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract error parameters
    const parameters = this.extractParameters(astNode.parameters);

    // Create error node
    const errorNode: ErrorNode = {
      id: nodeId,
      type: 'ERROR',
      name: astNode.name || 'unknown',
      ...(sourceLocation && { sourceLocation }),
      properties: {
        parameters,
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent (contract)
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    // Update analysis context
    this.context.updateAnalysisContext({
      // currentError is not part of the standard AnalysisContext
      // but we can add it as a custom property if needed
    });

    const result: ProcessorResult = {
      node: errorNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract error parameters from AST node
   */
  protected override extractParameters(
    parametersNode?: SolidityAstNode
  ): Array<{ name: string; type: string }> {
    if (!parametersNode || !parametersNode.parameters) {
      return [];
    }

    return (
      (parametersNode as any).parameters?.map((param: any) => ({
        name: param.name || 'unknown',
        type: param.typeDescriptions?.typeString || 'unknown',
      })) || []
    );
  }
}
