/**
 * Elementary Type Processor
 * Handles ElementaryTypeName AST nodes (uint256, address, bool, etc.)
 */

import { BaseProcessor, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { ExpressionNode } from '../../types/cpg';

export class ElementaryTypeProcessor extends BaseProcessor {
  /**
   * Check if this processor can handle the given AST node type
   */
  canProcess(nodeType: string): boolean {
    return nodeType === 'ElementaryTypeName';
  }

  /**
   * Get the priority of this processor
   */
  override getPriority(): number {
    return 35; // Lower priority - type information node
  }

  /**
   * Process ElementaryTypeName AST node
   */
  process(
    astNode: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const sourceLocation = this.context.extractSourceLocation(
      astNode,
      filePath
    );

    // Extract type information
    const typeName = this.extractTypeName(astNode);
    const typeCategory = this.categorizeType(typeName);

    // Create elementary type node as an expression
    const typeNode: ExpressionNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: typeName,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        expressionType: 'ElementaryTypeName',
        isTainted: false,
        taintSources: [],
        // Store additional data as value
        value: JSON.stringify({
          typeName,
          typeCategory,
          isElementaryType: true,
        }),
      },
    };

    // Create edges
    const edges = [];
    const childNodeIds: string[] = [];

    // Create CONTAINS edge from parent
    if (parentId) {
      edges.push(this.createContainsEdge(parentId, nodeId));
    }

    const result: ProcessorResult = {
      node: typeNode,
      edges,
      childNodeIds,
    };

    this.validateResult(result);
    return result;
  }

  /**
   * Extract type name from AST node
   */
  private extractTypeName(astNode: SolidityAstNode): string {
    const nodeAny = astNode as any;
    return (
      nodeAny.name ||
      nodeAny.typeDescriptions?.typeString ||
      nodeAny.typeName ||
      'unknown'
    );
  }

  /**
   * Categorize the elementary type
   */
  private categorizeType(typeName: string): string {
    if (typeName.startsWith('uint') || typeName.startsWith('int')) {
      return 'integer';
    }
    if (typeName === 'address') {
      return 'address';
    }
    if (typeName === 'bool') {
      return 'boolean';
    }
    if (typeName.startsWith('bytes')) {
      return 'bytes';
    }
    if (typeName === 'string') {
      return 'string';
    }
    return 'unknown';
  }
}
