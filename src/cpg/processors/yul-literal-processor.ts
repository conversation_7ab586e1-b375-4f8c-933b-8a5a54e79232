/**
 * Yul Literal Processor
 * Handles YulLiteral nodes (literal values in Yul assembly)
 */

import { BaseProcessor, ProcessorContext, ProcessorResult } from './base-processor';
import { SolidityAstNode } from '../../services/solidity-ast.service';
import { CpgNode, CpgEdge } from '../../types/cpg';

export class YulLiteralProcessor extends BaseProcessor {
  constructor(context: ProcessorContext) {
    super(context);
  }

  override getPriority(): number {
    return 39; // Priority close to other Yul processors
  }

  canProcess(nodeType: string): boolean {
    return nodeType === 'YulLiteral';
  }

  process(
    node: SolidityAstNode,
    filePath: string,
    parentId?: string
  ): ProcessorResult {
    const nodeId = this.context.generateNodeId();
    const edges: CpgEdge[] = [];

    // Extract literal information
    const literalInfo = this.extractLiteralInfo(node);

    // Create the Yul literal node
    const sourceLocation = this.context.extractSourceLocation(node, filePath);
    const cpgNode: CpgNode = {
      id: nodeId,
      type: 'EXPRESSION',
      name: `yul_literal_${literalInfo.value}`,
      ...(sourceLocation && { sourceLocation }),
      properties: {
        nodeType: node.nodeType,
        isYulLiteral: true,
        value: literalInfo.value,
        kind: literalInfo.kind,
        type: literalInfo.type,
        isNumeric: literalInfo.isNumeric,
        isString: literalInfo.isString,
        isBoolean: literalInfo.isBoolean,
      },
    };

    // Add CONTAINS edge from parent
    if (parentId) {
      edges.push({
        id: this.context.generateEdgeId(),
        type: 'CONTAINS',
        source: parentId,
        target: nodeId,
        properties: {
          relationship: 'yul_literal',
        },
      });
    }

    return {
      node: cpgNode,
      edges,
      childNodeIds: [], // No child nodes for literals
    };
  }

  private extractLiteralInfo(node: SolidityAstNode): {
    value: string;
    kind: string;
    type: string;
    isNumeric: boolean;
    isString: boolean;
    isBoolean: boolean;
  } {
    const nodeAny = node as any;
    const value = nodeAny.value || '0';
    const kind = nodeAny.kind || 'number';
    const type = nodeAny.type || 'uint256';
    
    return {
      value,
      kind,
      type,
      isNumeric: this.isNumericLiteral(kind, value),
      isString: kind === 'string',
      isBoolean: kind === 'bool',
    };
  }

  private isNumericLiteral(kind: string, value: string): boolean {
    if (kind === 'number') return true;
    if (kind === 'string') return false;
    if (kind === 'bool') return false;
    
    // Try to parse as number
    return !isNaN(Number(value));
  }
}
