/**
 * Taint Analyzer
 * Performs sophisticated taint analysis on the CPG
 */

import {
  CpgGraph,
  CpgNode,
  TaintEdge,
  TaintFlow,
  SinkInfo,
  AnalysisContext,
} from '../../types/cpg';

export interface TaintAnalysisResult {
  taintFlows: TaintFlow[];
  sinks: SinkInfo[];
  taintedNodes: Set<string>;
  sources: Set<string>;
}

export class TaintAnalyzer {
  private cpg: CpgGraph;

  constructor(cpg: CpgGraph, _context: AnalysisContext) {
    this.cpg = cpg;
  }

  /**
   * Perform comprehensive taint analysis
   */
  analyze(): TaintAnalysisResult {
    console.log('🔍 Starting comprehensive taint analysis...');

    // Step 1: Identify taint sources
    const sources = this.identifyTaintSources();
    console.log(`📊 Found ${sources.size} taint sources`);

    // Step 2: Identify potential sinks
    const sinks = this.identifySinks();
    console.log(`📊 Found ${sinks.length} potential sinks`);

    // Step 3: Perform taint propagation
    const taintedNodes = this.propagateTaint(sources);
    console.log(`📊 Taint propagated to ${taintedNodes.size} nodes`);

    // Step 4: Create taint flows
    const taintFlows = this.createTaintFlows(sources, sinks, taintedNodes);
    console.log(`📊 Created ${taintFlows.length} taint flows`);

    // Step 5: Create taint edges in CPG
    this.createTaintEdges(sources, taintedNodes);

    console.log('✅ Taint analysis complete');

    return {
      taintFlows,
      sinks,
      taintedNodes,
      sources,
    };
  }

  /**
   * Identify taint sources in the CPG
   */
  private identifyTaintSources(): Set<string> {
    const sources = new Set<string>();

    for (const [nodeId, node] of this.cpg.nodes) {
      if (this.isTaintSource(node)) {
        sources.add(nodeId);
        this.markNodeAsTainted(nodeId, this.getTaintSourceType(node));
      }
    }

    return sources;
  }

  /**
   * Identify potential sinks in the CPG
   */
  private identifySinks(): SinkInfo[] {
    const sinks: SinkInfo[] = [];

    for (const [nodeId, node] of this.cpg.nodes) {
      if (this.isSink(node)) {
        const sinkInfo: SinkInfo = {
          nodeId,
          sinkType: this.getSinkType(node),
          location: node.sourceLocation || {
            start: 0,
            length: 0,
            lines: [],
            startColumn: 0,
            endColumn: 0,
            filename: 'unknown',
          },
          taintSources: [], // Will be populated during flow analysis
        };
        sinks.push(sinkInfo);
      }
    }

    return sinks;
  }

  /**
   * Propagate taint through the CPG using data flow edges
   */
  private propagateTaint(sources: Set<string>): Set<string> {
    const taintedNodes = new Set<string>(sources);
    const workList = Array.from(sources);
    const visited = new Set<string>();

    while (workList.length > 0) {
      const currentNode = workList.pop()!;

      if (visited.has(currentNode)) {
        continue;
      }
      visited.add(currentNode);

      // Find all nodes that this node can taint
      const taintTargets = this.findTaintTargets(currentNode);

      for (const target of taintTargets) {
        if (!taintedNodes.has(target)) {
          taintedNodes.add(target);
          workList.push(target);
          this.markNodeAsTainted(target, 'propagated');
        }
      }
    }

    return taintedNodes;
  }

  /**
   * Create taint flows from sources to sinks
   */
  private createTaintFlows(
    sources: Set<string>,
    sinks: SinkInfo[],
    taintedNodes: Set<string>
  ): TaintFlow[] {
    const flows: TaintFlow[] = [];

    for (const sink of sinks) {
      if (taintedNodes.has(sink.nodeId)) {
        // Find which sources can reach this sink
        const reachingSources = this.findReachingSources(sink.nodeId, sources);

        for (const sourceId of reachingSources) {
          const path = this.findTaintPath(sourceId, sink.nodeId);
          const sourceNode = this.cpg.nodes.get(sourceId);
          const sinkNode = this.cpg.nodes.get(sink.nodeId);

          if (sourceNode && sinkNode && path.length > 0) {
            const flow: TaintFlow = {
              source: sourceNode.name,
              sink: sinkNode.name,
              path,
            };
            flows.push(flow);

            // Update sink with taint sources
            sink.taintSources.push(sourceNode.name);
          }
        }
      }
    }

    return flows;
  }

  /**
   * Create taint edges in the CPG
   */
  private createTaintEdges(
    sources: Set<string>,
    taintedNodes: Set<string>
  ): void {
    let edgeCount = 0;

    for (const sourceId of sources) {
      for (const taintedId of taintedNodes) {
        if (
          sourceId !== taintedId &&
          this.cpg.nodes.has(sourceId) &&
          this.cpg.nodes.has(taintedId) &&
          this.hasDirectTaintFlow(sourceId, taintedId)
        ) {
          const taintEdge: TaintEdge = {
            id: `taint_edge_${++edgeCount}`,
            type: 'TAINT',
            source: sourceId,
            target: taintedId,
            properties: {
              taintType: 'direct_flow',
              confidence: this.calculateTaintConfidence(sourceId, taintedId),
            },
          };

          this.cpg.edges.set(taintEdge.id, taintEdge);
        }
      }
    }
  }

  /**
   * Check if a node is a taint source
   */
  private isTaintSource(node: CpgNode): boolean {
    if (node.type === 'VARIABLE') {
      const varNode = node as any;
      // Parameters are taint sources
      if (varNode.properties.scope === 'PARAMETER') {
        return true;
      }
    }

    if (node.type === 'EXPRESSION') {
      // External function calls are taint sources
      if (
        node.properties['expressionType'] === 'FunctionCall' &&
        node.properties['isExternal']
      ) {
        return true;
      }
      // msg.* access is a taint source
      if (node.name.includes('msg.')) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a node is a sink
   */
  private isSink(node: CpgNode): boolean {
    if (node.type === 'ASSIGNMENT') {
      return node.properties['isSink'] === true;
    }

    if (node.type === 'VARIABLE') {
      const varNode = node as any;
      // State variables can be sinks
      return varNode.properties.isStateVariable;
    }

    if (node.type === 'EXPRESSION') {
      // External calls are sinks
      if (
        node.properties['expressionType'] === 'FunctionCall' &&
        node.properties['isExternal']
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get taint source type
   */
  private getTaintSourceType(node: CpgNode): string {
    if (node.type === 'VARIABLE') {
      const varNode = node as any;
      if (varNode.properties.scope === 'PARAMETER') {
        return 'user_input';
      }
    }

    if (node.type === 'EXPRESSION') {
      if (node.properties['expressionType'] === 'FunctionCall') {
        return 'external_call';
      }
      if (node.name.includes('msg.')) {
        return 'msg_context';
      }
    }

    return 'unknown';
  }

  /**
   * Get sink type
   */
  private getSinkType(
    node: CpgNode
  ): 'STATE_WRITE' | 'EXTERNAL_CALL' | 'STORAGE_WRITE' | 'EMIT_EVENT' {
    if (node.type === 'ASSIGNMENT') {
      return (node.properties['sinkType'] as any) || 'STORAGE_WRITE';
    }

    if (node.type === 'VARIABLE') {
      const varNode = node as any;
      if (varNode.properties.isStateVariable) {
        return 'STATE_WRITE';
      }
    }

    if (node.type === 'EXPRESSION') {
      if (node.properties['expressionType'] === 'FunctionCall') {
        return 'EXTERNAL_CALL';
      }
    }

    return 'STORAGE_WRITE';
  }

  /**
   * Find nodes that can be tainted by the given node
   */
  private findTaintTargets(nodeId: string): string[] {
    const targets: string[] = [];

    // Look for data flow edges from this node
    for (const [_edgeId, edge] of this.cpg.edges) {
      if (edge.source === nodeId) {
        switch (edge.type) {
          case 'WRITES':
          case 'DATA_FLOW':
          case 'CALLS':
            targets.push(edge.target);
            break;
        }
      }
    }

    return targets;
  }

  /**
   * Find sources that can reach the given sink
   */
  private findReachingSources(
    sinkId: string,
    sources: Set<string>
  ): Set<string> {
    const reaching = new Set<string>();

    for (const sourceId of sources) {
      if (this.canReach(sourceId, sinkId)) {
        reaching.add(sourceId);
      }
    }

    return reaching;
  }

  /**
   * Check if source can reach sink through taint propagation
   */
  private canReach(sourceId: string, sinkId: string): boolean {
    const visited = new Set<string>();
    const queue = [sourceId];

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current === sinkId) {
        return true;
      }

      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      const targets = this.findTaintTargets(current);
      queue.push(...targets);
    }

    return false;
  }

  /**
   * Find taint path from source to sink
   */
  private findTaintPath(sourceId: string, sinkId: string): string[] {
    // Simple BFS to find path
    const queue = [[sourceId]];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const path = queue.shift()!;
      const current = path[path.length - 1];

      if (current === sinkId) {
        return path;
      }

      if (current && visited.has(current)) {
        continue;
      }
      if (current) {
        visited.add(current);
      }

      const targets = current ? this.findTaintTargets(current) : [];
      for (const target of targets) {
        queue.push([...path, target]);
      }
    }

    return [];
  }

  /**
   * Check if there's a direct taint flow between two nodes
   */
  private hasDirectTaintFlow(sourceId: string, targetId: string): boolean {
    const targets = this.findTaintTargets(sourceId);
    return targets.includes(targetId);
  }

  /**
   * Calculate taint confidence score
   */
  private calculateTaintConfidence(sourceId: string, targetId: string): number {
    // Simple confidence calculation based on path length
    const path = this.findTaintPath(sourceId, targetId);
    if (path.length <= 2) return 0.9;
    if (path.length <= 4) return 0.7;
    if (path.length <= 6) return 0.5;
    return 0.3;
  }

  /**
   * Mark a node as tainted
   */
  private markNodeAsTainted(nodeId: string, taintSource: string): void {
    const node = this.cpg.nodes.get(nodeId);
    if (node && node.type === 'VARIABLE') {
      const varNode = node as any;
      varNode.properties.isTainted = true;
      varNode.properties.taintSource = taintSource;
    }
  }
}
