/**
 * Call Graph Analyzer
 * Builds comprehensive call graph for the CPG
 */

import {
  CpgGraph,
  CpgNode,
  FunctionNode,
  CallGraphEdge,
  AnalysisContext,
} from '../../types/cpg';

export interface CallGraphResult {
  callGraph: Map<string, CallSite[]>;
  entryPoints: string[];
  recursiveFunctions: string[];
  callDepth: Map<string, number>;
}

export interface CallSite {
  caller: string;
  callee: string;
  callType: 'DIRECT' | 'INDIRECT' | 'EXTERNAL' | 'DELEGATE';
  location: string;
  gasLimit?: number;
  value?: string;
}

export class CallGraphAnalyzer {
  private cpg: CpgGraph;

  constructor(cpg: CpgGraph, _context: AnalysisContext) {
    this.cpg = cpg;
  }

  /**
   * Build comprehensive call graph
   */
  analyze(): CallGraphResult {
    console.log('📞 Starting call graph analysis...');

    // Step 1: Identify all functions
    const functions = this.identifyFunctions();
    console.log(`📊 Found ${functions.length} functions`);

    // Step 2: Build call graph
    const callGraph = this.buildCallGraph(functions);
    console.log(`📊 Built call graph with ${callGraph.size} callers`);

    // Step 3: Identify entry points
    const entryPoints = this.identifyEntryPoints(functions, callGraph);
    console.log(`📊 Found ${entryPoints.length} entry points`);

    // Step 4: Detect recursive functions
    const recursiveFunctions = this.detectRecursiveFunctions(callGraph);
    console.log(`📊 Found ${recursiveFunctions.length} recursive functions`);

    // Step 5: Calculate call depths
    const callDepth = this.calculateCallDepths(entryPoints, callGraph);
    console.log(`📊 Calculated call depths for ${callDepth.size} functions`);

    // Step 6: Create call graph edges in CPG
    this.createCallGraphEdges(callGraph);

    console.log('✅ Call graph analysis complete');

    return {
      callGraph,
      entryPoints,
      recursiveFunctions,
      callDepth,
    };
  }

  /**
   * Identify all functions in the CPG
   */
  private identifyFunctions(): FunctionNode[] {
    const functions: FunctionNode[] = [];

    for (const [_nodeId, node] of this.cpg.nodes) {
      if (node.type === 'FUNCTION') {
        functions.push(node as FunctionNode);
      }
    }

    return functions;
  }

  /**
   * Build call graph from function calls
   */
  private buildCallGraph(functions: FunctionNode[]): Map<string, CallSite[]> {
    const callGraph = new Map<string, CallSite[]>();

    // Initialize call graph
    for (const func of functions) {
      callGraph.set(func.id, []);
    }

    // Find all function calls
    for (const [nodeId, node] of this.cpg.nodes) {
      if (this.isFunctionCall(node)) {
        const callSite = this.createCallSite(node, nodeId);
        if (callSite) {
          const caller = this.findContainingFunction(nodeId);
          if (caller && callGraph.has(caller)) {
            callGraph.get(caller)!.push(callSite);
          }
        }
      }
    }

    return callGraph;
  }

  /**
   * Identify entry points (public/external functions)
   */
  private identifyEntryPoints(
    functions: FunctionNode[],
    _callGraph: Map<string, CallSite[]>
  ): string[] {
    const entryPoints: string[] = [];

    for (const func of functions) {
      if (this.isEntryPoint(func)) {
        entryPoints.push(func.id);
      }
    }

    return entryPoints;
  }

  /**
   * Detect recursive functions
   */
  private detectRecursiveFunctions(
    callGraph: Map<string, CallSite[]>
  ): string[] {
    const recursive: string[] = [];

    for (const [functionId, _callSites] of callGraph) {
      if (this.isRecursive(functionId, callGraph, new Set())) {
        recursive.push(functionId);
      }
    }

    return recursive;
  }

  /**
   * Calculate call depths from entry points
   */
  private calculateCallDepths(
    entryPoints: string[],
    callGraph: Map<string, CallSite[]>
  ): Map<string, number> {
    const depths = new Map<string, number>();

    // Initialize all functions with infinite depth
    for (const functionId of callGraph.keys()) {
      depths.set(functionId, Infinity);
    }

    // Set entry points to depth 0
    for (const entryPoint of entryPoints) {
      depths.set(entryPoint, 0);
    }

    // BFS to calculate depths
    const queue = [...entryPoints];
    const visited = new Set<string>();

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      const currentDepth = depths.get(current) || 0;
      const callSites = callGraph.get(current) || [];

      for (const callSite of callSites) {
        const calleeDepth = depths.get(callSite.callee) || Infinity;
        const newDepth = currentDepth + 1;

        if (newDepth < calleeDepth) {
          depths.set(callSite.callee, newDepth);
          queue.push(callSite.callee);
        }
      }
    }

    return depths;
  }

  /**
   * Create call graph edges in the CPG
   */
  private createCallGraphEdges(callGraph: Map<string, CallSite[]>): void {
    let edgeCount = 0;

    for (const [caller, callSites] of callGraph) {
      for (const callSite of callSites) {
        // Only create edges between existing nodes
        if (this.cpg.nodes.has(caller) && this.cpg.nodes.has(callSite.callee)) {
          const callEdge: CallGraphEdge = {
            id: `callgraph_${++edgeCount}`,
            type: 'CALL_GRAPH',
            source: caller,
            target: callSite.callee,
            properties: {
              callType: callSite.callType,
              ...(callSite.gasLimit && { gasLimit: callSite.gasLimit }),
              ...(callSite.value && { value: callSite.value }),
            },
          };

          this.cpg.edges.set(callEdge.id, callEdge);
        }
      }
    }
  }

  /**
   * Check if a node represents a function call
   */
  private isFunctionCall(node: CpgNode): boolean {
    return (
      node.type === 'EXPRESSION' &&
      node.properties['expressionType'] === 'FunctionCall'
    );
  }

  /**
   * Create call site from function call node
   */
  private createCallSite(node: CpgNode, nodeId: string): CallSite | null {
    if (!this.isFunctionCall(node)) {
      return null;
    }

    const caller = this.findContainingFunction(nodeId);
    if (!caller) {
      return null;
    }

    const callee = this.resolveFunctionCall(node);
    if (!callee) {
      return null;
    }

    const callType = this.determineCallType(node);

    const result: any = {
      caller,
      callee,
      callType,
      location: nodeId,
    };

    if (node.properties['gasLimit']) {
      result.gasLimit = node.properties['gasLimit'];
    }

    if (node.properties['value']) {
      result.value = node.properties['value'];
    }

    return result;
  }

  /**
   * Find the function that contains the given node
   */
  private findContainingFunction(nodeId: string): string | null {
    // Traverse up the containment hierarchy to find the function
    for (const [_edgeId, edge] of this.cpg.edges) {
      if (edge.target === nodeId && edge.type === 'CONTAINS') {
        const parent = this.cpg.nodes.get(edge.source);
        if (parent?.type === 'FUNCTION') {
          return edge.source;
        }
        // Recursively check parent
        return this.findContainingFunction(edge.source);
      }
    }
    return null;
  }

  /**
   * Resolve function call to target function
   */
  private resolveFunctionCall(node: CpgNode): string | null {
    const functionName = node.properties['functionName'];
    if (!functionName) {
      return null;
    }

    // Find function by name
    for (const [nodeId, funcNode] of this.cpg.nodes) {
      if (funcNode.type === 'FUNCTION' && funcNode.name === functionName) {
        return nodeId;
      }
    }

    // If not found, don't create placeholder - return null to avoid invalid edges
    return null;
  }

  /**
   * Determine the type of function call
   */
  private determineCallType(
    node: CpgNode
  ): 'DIRECT' | 'INDIRECT' | 'EXTERNAL' | 'DELEGATE' {
    if (node.properties['isExternal']) {
      return 'EXTERNAL';
    }

    // Check for delegate calls
    if (node.name.includes('delegatecall')) {
      return 'DELEGATE';
    }

    // Check for indirect calls (function pointers, etc.)
    if (node.properties['indirect']) {
      return 'INDIRECT';
    }

    return 'DIRECT';
  }

  /**
   * Check if a function is an entry point
   */
  private isEntryPoint(func: FunctionNode): boolean {
    const visibility = func.properties.visibility;

    // Public and external functions are entry points
    if (visibility === 'public' || visibility === 'external') {
      return true;
    }

    // Constructor is an entry point
    if (func.name === 'constructor') {
      return true;
    }

    // Fallback and receive functions are entry points
    if (func.name === 'fallback' || func.name === 'receive') {
      return true;
    }

    return false;
  }

  /**
   * Check if a function is recursive
   */
  private isRecursive(
    functionId: string,
    callGraph: Map<string, CallSite[]>,
    visited: Set<string>
  ): boolean {
    if (visited.has(functionId)) {
      return true; // Found a cycle
    }

    visited.add(functionId);

    const callSites = callGraph.get(functionId) || [];
    for (const callSite of callSites) {
      if (this.isRecursive(callSite.callee, callGraph, new Set(visited))) {
        return true;
      }
    }

    visited.delete(functionId);
    return false;
  }

  /**
   * Get all functions called by a given function
   */
  getCallees(functionId: string, callGraph: Map<string, CallSite[]>): string[] {
    const callSites = callGraph.get(functionId) || [];
    return callSites.map((site) => site.callee);
  }

  /**
   * Get all functions that call a given function
   */
  getCallers(functionId: string, callGraph: Map<string, CallSite[]>): string[] {
    const callers: string[] = [];

    for (const [caller, callSites] of callGraph) {
      if (callSites.some((site) => site.callee === functionId)) {
        callers.push(caller);
      }
    }

    return callers;
  }

  /**
   * Check if one function can reach another through calls
   */
  canReach(
    fromFunction: string,
    toFunction: string,
    callGraph: Map<string, CallSite[]>
  ): boolean {
    const visited = new Set<string>();
    const queue = [fromFunction];

    while (queue.length > 0) {
      const current = queue.shift()!;

      if (current === toFunction) {
        return true;
      }

      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      const callees = this.getCallees(current, callGraph);
      queue.push(...callees);
    }

    return false;
  }
}
