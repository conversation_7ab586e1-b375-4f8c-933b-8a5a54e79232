// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

// src/test/test-contracts/Bruh.sol

contract Bruh {
    uint public valMMM;
    
    
    function setBoom(uint _value) public {
        valMMM = _value;
    }
}

// src/test/test-contracts/CpgTestContract.sol

/**
 * Comprehensive test contract for CPG analysis
 * Tests state variables, local variables, functions, and taint flows
 */
contract CpgTestContract {
    // State variables (should be marked as STATE scope)
    uint256 public stateValue;
    address private owner;
    mapping(address => uint256) public balances;
    
    // Events for sink detection
    event ValueChanged(uint256 newValue);
    event Transfer(address from, address to, uint256 amount);
    
    constructor(uint256 initialValue) {
        stateValue = initialValue;  // State assignment (sink)
        owner = msg.sender;         // State assignment (sink)
    }
    
    // Simple function with parameters and local variables
    function testFunction(uint256 a, uint256 b) public pure returns (uint256) {
        uint256 localSum = a + b;      // Local variable with expression
        uint256 localProduct = a * b;  // Another local variable
        return localSum + localProduct; // Return expression
    }
    
    // Function with state interaction and taint flow
    function updateValue(uint256 userInput) public {
        uint256 processedInput = userInput * 2;  // Local variable (tainted from parameter)
        stateValue = processedInput;             // State write (sink) - taint flows here
        emit ValueChanged(processedInput);       // Event emission (sink)
    }
    
    // Function with complex taint flow
    function transferFunds(address to, uint256 amount) public {
        require(msg.sender == owner, "Not owner");
        
        uint256 senderBalance = balances[msg.sender];  // State read
        uint256 newBalance = senderBalance - amount;   // Local calculation (tainted)
        
        balances[msg.sender] = newBalance;             // State write (sink)
        balances[to] = balances[to] + amount;          // State write (sink)
        
        emit Transfer(msg.sender, to, amount);         // Event emission (sink)
    }
    
    // Function with multiple parameter types
    function complexFunction(
        uint256 numParam,
        address addrParam, 
        bool boolParam
    ) public view returns (uint256, address, bool) {
        uint256 localNum = numParam + 1;     // Local variable (tainted from parameter)
        address localAddr = addrParam;       // Local variable (tainted from parameter)
        bool localBool = !boolParam;         // Local variable (tainted from parameter)
        
        return (localNum, localAddr, localBool);
    }
    
    // Getter function (should read state)
    function getOwner() public view returns (address) {
        return owner;  // State read
    }
    
    // Function with nested expressions
    function nestedCalculation(uint256 x, uint256 y) public pure returns (uint256) {
        uint256 step1 = x + y;           // Local variable
        uint256 step2 = step1 * 2;       // Local variable depending on another local
        uint256 result = step2 + x;      // Local variable with mixed dependencies
        return result;
    }
}

// src/test/test-contracts/MultipleModifiers.sol

/**
 * Test contract for multiple modifier support
 * Tests various modifier combinations and invocations
 */
contract MultipleModifiers {
    address public owner;
    bool public paused;
    uint256 public counter;
    mapping(address => bool) public authorized;
    
    // Events for testing
    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);
    event Paused(address account);
    event Unpaused(address account);
    event CounterIncremented(uint256 newValue);
    
    constructor() {
        owner = msg.sender;
        paused = false;
        counter = 0;
    }
    
    // Modifier definitions
    modifier onlyOwner() {
        require(msg.sender == owner, "Not the owner");
        _;
    }
    
    modifier whenNotPaused() {
        require(!paused, "Contract is paused");
        _;
    }
    
    modifier whenPaused() {
        require(paused, "Contract is not paused");
        _;
    }
    
    modifier onlyAuthorized() {
        require(authorized[msg.sender] || msg.sender == owner, "Not authorized");
        _;
    }
    
    modifier validAddress(address _addr) {
        require(_addr != address(0), "Invalid address");
        _;
    }
    
    modifier nonZeroAmount(uint256 _amount) {
        require(_amount > 0, "Amount must be greater than zero");
        _;
    }
    
    // Functions with single modifiers
    function transferOwnership(address newOwner) public onlyOwner validAddress(newOwner) {
        address previousOwner = owner;
        owner = newOwner;
        emit OwnershipTransferred(previousOwner, newOwner);
    }
    
    function pause() public onlyOwner whenNotPaused {
        paused = true;
        emit Paused(msg.sender);
    }
    
    function unpause() public onlyOwner whenPaused {
        paused = false;
        emit Unpaused(msg.sender);
    }
    
    // Functions with multiple modifiers (2 modifiers)
    function authorizeUser(address user) public onlyOwner validAddress(user) {
        authorized[user] = true;
    }
    
    function revokeUser(address user) public onlyOwner validAddress(user) {
        authorized[user] = false;
    }
    
    // Functions with multiple modifiers (3 modifiers)
    function incrementCounter() public onlyAuthorized whenNotPaused {
        counter++;
        emit CounterIncremented(counter);
    }
    
    function setCounter(uint256 newValue) public onlyOwner whenNotPaused nonZeroAmount(newValue) {
        counter = newValue;
        emit CounterIncremented(counter);
    }
    
    // Functions with multiple modifiers (4 modifiers) - complex case
    function complexOperation(address target, uint256 amount) 
        public 
        onlyOwner 
        whenNotPaused 
        validAddress(target) 
        nonZeroAmount(amount) 
    {
        // Complex operation with multiple checks
        authorized[target] = true;
        counter += amount;
        emit CounterIncremented(counter);
    }
    
    // Function with modifier that has multiple parameters
    modifier inRange(uint256 value, uint256 min, uint256 max) {
        require(value >= min && value <= max, "Value out of range");
        _;
    }
    
    function setCounterInRange(uint256 newValue) public onlyOwner inRange(newValue, 1, 1000) {
        counter = newValue;
        emit CounterIncremented(counter);
    }
    
    // Function with nested modifier calls
    function emergencyStop() public onlyOwner {
        if (!paused) {
            pause();
        }
    }
    
    // View functions with modifiers
    function getOwnerInfo() public view onlyAuthorized returns (address, uint256) {
        return (owner, counter);
    }
    
    // Pure function with modifier (edge case)
    function calculateWithAuth(uint256 a, uint256 b) public pure nonZeroAmount(a) returns (uint256) {
        return a + b;
    }
}

// src/test/test-contracts/SimpleState.sol

contract SimpleState {
    // State variable
    uint256 public stateValue;
    
    // Local variable assignment with state interaction
    function setValue(uint256 newValue) public {
        uint256 localValue = newValue * 2; // Local variable with expression
        stateValue = localValue; // State assignment (potential sink)
    }
    
    // Function with taint flow
    function processValue(uint256 userInput) public returns (uint256) {
        uint256 processed = userInput + 10; // Tainted local variable
        stateValue = processed; // Taint flows to state (sink)
        return processed;
    }
}

// src/test/test-contracts/Meh.sol

contract Meh {
    uint public valueXYZ;
    
    constructor() {
        valueXYZ = 42;
    }
    
    function setValueXYZ(uint _value) public {
        valueXYZ = _value;
    }
}

// src/test/test-contracts/TestContract.sol

contract TestContract {
    uint public value;
    Meh public mehContract;
    
    constructor() {
        mehContract = new Meh();
    }
    
    function setValue(uint _value) public {
        value = _value;
    }
    
    function getValue() public view returns (uint) {
        return value;
    }
    
    function getMehValue() public view returns (uint) {
        return mehContract.valueXYZ();
    }
}

// src/test/test-contracts/Wrapper.sol

contract Wrapper {}
