
        pragma solidity ^0.8.0;

        contract IntegratedTest {
          uint256 public balance;

          function processPayment(uint256 amount) public {
            uint256 fee = calculateFee(amount); // Call graph + data flow
            uint256 total = amount + fee; // Data flow
            balance = total; // Taint flow to sink
          }

          function calculateFee(uint256 amount) internal pure returns (uint256) {
            return amount / 100; // 1% fee
          }
        }
      