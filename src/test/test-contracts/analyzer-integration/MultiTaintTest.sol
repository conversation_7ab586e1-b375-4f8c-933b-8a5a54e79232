
        pragma solidity ^0.8.0;

        contract MultiTaintTest {
          uint256 public balance1;
          uint256 public balance2;

          function multiTransfer(uint256 amount1, uint256 amount2) public {
            uint256 input1 = amount1;
            uint256 input2 = amount2;
            balance1 = input1; // First taint flow
            balance2 = input2; // Second taint flow
          }
        }
      